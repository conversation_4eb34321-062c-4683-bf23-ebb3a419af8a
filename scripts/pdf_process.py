import argparse
import asyncio
import base64
import os
import shutil
import tempfile
import tqdm
import logging

import aiofiles
import pymupdf4llm
import numpy as np

from ira_chat.config.shared_config import SharedConfig
from ira_chat.services import detection
from ira_chat.services.file_formats import save_to_tmp_file
from ira_chat.services.llm import init_llm_class
from ira_chat.utils import utils


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('file')
    parser.add_argument('--llm-service', default='openai')
    parser.add_argument('-m', '--model', default='gpt-4.1-mini')
    parser.add_argument('-o', '--output', default='output.md')
    parser.add_argument('-md', '--md-text', default=None)
    parser.add_argument('--img-dir', default=None)
    parser.add_argument('--inline-imgs', action='store_true')
    parser.add_argument('--skip-images', default=False, action='store_true')
    parser.add_argument('--log-output', action='store_true')
    parser.add_argument('--workers', default=8, type=int)
    parser.add_argument(
        '--context-chars', default=1000, type=int,
        help='Number of characters to extract as context around each image (default: 1000)'
    )
    return parser.parse_args()


def get_org_config(service: str):
    return {
        "openai": {
            "api_key": os.getenv("OPENAI_API_KEY"),
        },
        "llm_type": service,
        "vectorstore": {
            "type": "qdrant"
        },
        "gemini": {
            "gemini_api_key": os.getenv("GEMINI_API_KEY"),
        },
        "voyage": {
            "voyage_api_key": os.getenv("VOYAGE_API_KEY"),
        },
        "anthropic": {
            "anthropic_api_key": os.getenv("ANTHROPIC_API_KEY"),
            "embedding_provider": "openai",
        },
        "mistralai": {
            "mistral_api_key": os.getenv("MISTRAL_API_KEY"),
        },
        "groq": {
            "groq_api_key": os.getenv("GROQ_API_KEY"),
            "embedding_provider": "openai",
        }
    }


IMAGE_PROMPT = """
You are analyzing an image from technical documentation to generate descriptive text that will seamlessly integrate into the documentation flow. 
The output must be in {language} and formatted as natural documentation content without code block delimiters.

{context_section}

Your task is to describe the visual elements in a way that serves the documentation's purpose - helping users understand interfaces, controls, processes, or components shown in the image.

Guidelines:
- Write as natural documentation text, not as an image description
- Focus on actionable elements: buttons, controls, interfaces, diagrams, workflows
- Describe visual elements that users need to identify or interact with
- Use clear, precise terminology that matches technical documentation style
- Include all visible text, labels, and important visual indicators
- Organize information logically (e.g., top to bottom, left to right, or by functional groups)
- Describe spatial relationships when relevant for user understanding
- Focus on information that helps users navigate, configure, or understand the system
- Use the surrounding context to understand what the image is meant to illustrate
- Match the tone and terminology used in the surrounding documentation

Format your response as flowing documentation text that reads naturally when inserted between other paragraphs. 
Avoid phrases like "The image shows" or "This screenshot depicts" - write as if describing the actual interface or component directly.

Examples of good integration:
- Instead of: "The image shows a dialog with three buttons"
- Write: "The dialog contains three buttons: Save, Cancel, and Apply"

- Instead of: "This screenshot displays the main menu"
- Write: "The main menu provides access to File, Edit, View, and Tools options"

Rule: The output must be in {language}.
Rule: If the image contains no significant technical information (e.g., decorative elements, logos, blank spaces), output "[IMAGE OF NO SIGNIFICANT INFO]".
Rule: Extract and include all visible text exactly as it appears.
Rule: Focus on elements that users would need to reference or interact with in the context of using the documented system.
Rule: Use the provided context to better understand what aspects of the image are most relevant to describe.
"""

logger = logging.getLogger(__name__)


def extract_surrounding_context(md_text: str, img_placeholder: str, context_chars: int = 1000) -> str:
    """
    Extract surrounding text context around an image placeholder.

    Args:
        md_text: The full markdown text
        img_placeholder: The image placeholder to find (e.g., "![image.png](image.png)")
        context_chars: Number of characters to extract before and after the placeholder

    Returns:
        Formatted context string for the prompt
    """
    img_pos = md_text.find(img_placeholder)
    if img_pos == -1:
        return "CONTEXT: No surrounding context available."

    # Extract context before and after the image
    start_pos = max(0, img_pos - context_chars)
    end_pos = min(len(md_text), img_pos + len(img_placeholder) + context_chars)

    before_text = md_text[start_pos:img_pos].strip()
    after_text = md_text[img_pos + len(img_placeholder):end_pos].strip()

    # Clean up the context - remove excessive whitespace and partial lines
    if before_text:
        # Find the last complete sentence or paragraph
        before_lines = before_text.split('\n')
        if len(before_lines) > 1 and len(before_lines[-1]) < 50:  # Likely partial line
            before_text = '\n'.join(before_lines[:-1])

    if after_text:
        # Find the first complete sentence or paragraph
        after_lines = after_text.split('\n')
        if len(after_lines) > 1 and len(after_lines[0]) < 50:  # Likely partial line
            after_text = '\n'.join(after_lines[1:])

    context_parts = []
    if before_text:
        context_parts.append(f"TEXT BEFORE IMAGE:\n{before_text}")
    if after_text:
        context_parts.append(f"TEXT AFTER IMAGE:\n{after_text}")

    if context_parts:
        return "CONTEXT: The image appears in the following documentation context:\n\n" + "\n\n".join(context_parts)
    else:
        return "CONTEXT: No surrounding context available."


async def process_one(
    img_path: str, init_llm, llm, language,
    output_path,
    inline_imgs: bool,
    lock: asyncio.Lock,
    q: asyncio.Queue,
    context_chars: int = 1000,
    **kwargs
):
    async with aiofiles.open(img_path, 'rb') as f:
        img_data = await f.read()

    # Get current markdown content to extract context
    async with lock:
        async with aiofiles.open(output_path, 'r') as f:
            md_text = await f.read()

    # Extract surrounding context for better image analysis
    img_placeholder = f"![{img_path}]({img_path})"
    context = extract_surrounding_context(md_text, img_placeholder, context_chars)

    # Format the prompt with context
    formatted_prompt = IMAGE_PROMPT.format(
        language=language,
        context_section=context
    )

    max_attempts = 10
    for attempt in range(max_attempts):
        docs = await detection.extract_document_data(
            img_path, img_data, init_llm.llm_type, llm, doc_prompt=formatted_prompt
        )
        doc = docs[0]
        if 'unable' not in doc.page_content.lower() and "can't assist" not in doc.page_content.lower():
            break
        logger.warning(f'Failed to extract text from image: {doc.page_content}; Attempt {attempt + 1}')
    else:
        await q.put((img_path, False))
        logger.error(f'Failed to extract text from image {img_path} after {max_attempts} attempts')
        return

    # Replace image with extracted text
    async with lock:
        async with aiofiles.open(output_path, 'r') as f:
            md_text = await f.read()

        if inline_imgs:
            output_with_image = output_path.replace('.md', '_images.md')
            try:
                async with aiofiles.open(output_with_image, 'r') as f:
                    md_text_img = await f.read()
            except FileNotFoundError:
                md_text_img = md_text
            encoded_string = base64.b64encode(img_data).decode('utf-8')
            extension = detection.detect_extension(img_data[:4].hex())
            img_tag = f'<img src="data:image/{extension[1:]};base64,{encoded_string}" alt="{img_path}" />'
            repl_with_image = f'\n{img_tag}\n{doc.page_content}\n'

            md_text_imgs = md_text_img.replace(f"![{img_path}]({img_path})", repl_with_image)
            # Save md_text to output as intermediate result
            async with aiofiles.open(output_with_image, 'w') as f:
                await f.write(md_text_imgs)

        repl = f'\n{doc.page_content}\n'
        new_md_text = md_text.replace(f"![{img_path}]({img_path})", repl)
        # Save md_text to output as intermediate result
        async with aiofiles.open(output_path, 'w') as f:
            await f.write(new_md_text)

    # Remove img_path to keep only unprocessed to continue in case of restart
    os.remove(img_path)
    await q.put((img_path, True))


async def process_many(
    image_paths: list, img_dir: str, init_llm, llm, language,
    output_path,
    inline_imgs: bool,
    lock,
    q: asyncio.Queue,
    **kwargs
):
    for img_path in image_paths:
        img_path = os.path.join(img_dir, img_path)

        await process_one(
            img_path, init_llm, llm, language, output_path, inline_imgs, lock, q, **kwargs
        )


async def main():
    utils.setup_logging()
    args = parse_args()

    logging.getLogger('ira_chat.services.tesseract').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)

    if not args.log_output:
        logging.getLogger('ira_chat.services.detection').setLevel(logging.WARNING)

    cleanups = []
    img_dir = args.img_dir

    if img_dir is None:
        img_dir = tempfile.mkdtemp()
        cleanups.append(lambda: shutil.rmtree(img_dir))
    else:
        os.makedirs(img_dir, exist_ok=True)

    doc_converter = SharedConfig().doc_converter
    base, extension = os.path.splitext(args.file)
    if extension not in ['.doc', '.docx', '.pdf']:
        raise ValueError(f"Unsupported file format: {extension}")
    if extension in ['.doc', '.docx']:
        pdf_data = doc_converter.convert_file(
            args.file, extension.removeprefix('.'), 'pdf', open(args.file, 'rb').read()
        )
        pdf_path = save_to_tmp_file(pdf_data, '.pdf')
        cleanups.append(lambda: os.remove(pdf_path))

        args.file = pdf_path
    try:
        md_text_file = args.md_text
        if md_text_file is None or not os.path.exists(args.md_text):
            logger.info(f"Converting PDF to markdown [with_images={not args.skip_images}]: {args.file}")
            md_text = pymupdf4llm.to_markdown(
                args.file,
                write_images=not args.skip_images,
                image_path=img_dir,
                dpi=300,
                image_min_relative_size=0.05,
            )
            logger.info(f"Done converting PDF to markdown: {args.file}")
            with open(args.output, 'w') as f:
                f.write(md_text)
            if args.inline_imgs:
                with open(args.output.replace('.md', '_images.md'), 'w') as f:
                    f.write(md_text)
        else:
            with open(md_text_file, 'r') as f:
                md_text = f.read()

        image_paths = os.listdir(img_dir) if not args.skip_images else []

        need_llm = len(image_paths) > 0
        if need_llm:
            # Init LLM
            org_config = get_org_config(args.llm_service)
            params = {
                "model_name": args.model,
                "temperature": 0.7,
                "request_timeout": 300,
                "skip_callbacks": True,
            }
            init_llm = init_llm_class(org_config, params, None, None)
            llm = init_llm.init_llm(params)
            # Done init LLM
            doc_language = await llm.ainvoke(
                f"Detect language of the text snippet. Output must contain only an English language name "
                f"(e.g. if doc is Español then output Spanish).\n\n {md_text[:1000]}"
            )
            language = doc_language.content.strip().capitalize()
            logger.info(f"Detected language: {language}")

            workers = args.workers
            chunks = np.array_split(image_paths, workers)
            q = asyncio.Queue()
            lock = asyncio.Lock()
            tasks = []
            for chunk in chunks:
                t = asyncio.create_task(
                    process_many(
                        chunk.tolist(), img_dir, init_llm, llm, language,
                        args.output, args.inline_imgs, lock, q, context_chars=args.context_chars
                    )
                )
                tasks.append(t)

            bad = []
            for _ in tqdm.tqdm(image_paths):
                path, is_success = await q.get()
                if not is_success:
                    bad.append(path)

            if bad:
                logger.warning(f"Failed to extract text from images:")
                for path in bad:
                    logger.warning(path)

            await asyncio.gather(*tasks)

    finally:
        for cleanup in cleanups:
            cleanup()

    logger.info(f"Output saved to {args.output}")


if __name__ == '__main__':
    asyncio.run(main())
