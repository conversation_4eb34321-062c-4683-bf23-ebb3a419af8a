# WAP Chat Analysis Script

This script analyzes WAP chat data by pharmacy and user, providing comprehensive insights into question distribution, user behavior, and pharmacy engagement patterns.

## Features

### Data Analysis
- **Pharmacy Analysis**: Number of questions per pharmacy, users per pharmacy, top performing pharmacies
- **User Analysis**: Questions per user, multi-pharmacy users, user engagement patterns
- **Answer Quality Analysis**: Grade selector analysis, correct answer rates, quality by pharmacy/tags
- **Tags Analysis**: WAP vs Non-WAP distribution, tag patterns by pharmacy/user
- **Summary Statistics**: Total questions, unique pharmacies/users, averages and distributions

### Visualization Options
1. **Static Mode**: Generates matplotlib charts saved as PNG files
2. **Interactive Dashboard**: Streamlit-based web interface with interactive charts and data exploration

### Enhanced Sample Data Explorer
- **Multi-dimensional filtering**: Pharmacy, user, answer quality, and tags
- **Advanced sorting options**: Date (newest/oldest first), pharmacy ID, user ID, answer quality, or random
- **Flexible display options**: Show/hide metadata, full/preview answers, configurable sample sizes
- **Active filter summary**: Clear indication of applied filters
- **Chronological sorting**: Default newest-first for trend analysis
- Interactive exploration with rich metadata display
- Export analysis results to CSV files

## Usage

### Basic Analysis (Static Charts)
```bash
python scripts/wap_analysis.py -f WAP_Assistance_PROD_chats_2025-7-22.csv --mode static --export-csv
```

### Interactive Dashboard (Optimized with Caching)
```bash
# Install optional dependencies (if not already installed)
pip install streamlit seaborn

# Launch interactive dashboard
streamlit run scripts/wap_analysis.py -- -f WAP_Assistance_PROD_chats_2025-7-22.csv
```

**Performance Optimization**: The dashboard uses Streamlit's `@st.cache_data` decorator to cache all data loading and analysis operations. This means:
- **First load**: Takes ~3-5 seconds to process 6,441 records
- **Subsequent interactions**: Instant response when changing filters, sample sizes, or display options
- **Cache invalidation**: Automatically clears when the data file changes
- **Clean implementation**: Direct Streamlit imports, no conditional logic or workarounds
- **Fixed caching**: `st.set_page_config()` moved to top-level to prevent conflicts with cache decorators

### Sample Data Explorer Features

The enhanced sample data explorer provides powerful filtering and sorting capabilities:

#### Sorting Options
- **Date (Newest First)** - Default, shows most recent questions first (ideal for trend analysis)
- **Date (Oldest First)** - Historical perspective, oldest questions first
- **Pharmacy ID** - Alphabetical by pharmacy identifier
- **User ID** - Alphabetical by user identifier
- **Answer Quality** - Ordered by quality: Correct → Partial → Incorrect → No Answer → Unknown → Excluded
- **Random** - Random sampling for unbiased analysis

#### Display Options
- **Sample Size**: 5, 10, 20, 50, or 100 questions
- **Show Metadata**: Toggle detailed information (quality, tags, IDs, dates)
- **Show Full Answers**: Toggle between full answers and 200-character previews

#### Filtering Capabilities
- **Pharmacy**: Select specific pharmacy or view all
- **User**: Select specific user or view all
- **Answer Quality**: Filter by Correct, Incorrect, Partial, No Answer, Excluded, Unknown
- **Tags**: Filter by WAP, Not WAP, or No Tag
- **No Answer Generated**: Filter by All, Exclude, or Only "i-do-not-even-know-what-to-answer-to-this" responses

## Performance Optimization

The dashboard is optimized for fast, responsive interaction using Streamlit's caching system:

### Caching Strategy
- **Data Loading**: `@st.cache_data` on CSV loading and processing
- **Analysis Functions**: All analysis operations (pharmacy, user, quality, tags) are cached
- **Centralized Loading**: Single cached function loads all analysis data
- **Smart Invalidation**: Cache automatically clears when the source file changes
- **Clean Architecture**: Direct Streamlit imports, no conditional logic

### Performance Benefits
- **Initial Load**: ~3-5 seconds for 6,441 records (one-time cost)
- **UI Interactions**: Instant response when changing:
  - Sample size (5 → 10 → 20 → 50 → 100)
  - Sorting options (Date, Pharmacy ID, User ID, Quality, Random)
  - Filters (Pharmacy, User, Quality, Tags, No Answer Generated)
  - Display toggles (metadata, full answers)

### Cache Management
- **Automatic**: No manual cache clearing needed
- **File-based**: Cache invalidates when CSV file is modified
- **Memory Efficient**: Uses Streamlit's optimized storage manager

### Command Line Options
- `-f, --file`: Path to the CSV file (required)
- `-o, --output`: Output directory for results (default: 'wap_analysis_results')
- `--mode`: Analysis mode - 'streamlit' or 'static' (default: 'streamlit')
- `--export-csv`: Export analysis results to CSV files

## Data Format

The script expects a CSV file with the following key columns:
- `chat_title`: Format "user:358794_company:57377_demand:211833"
- `question`: The user's question
- `answer`: The system's answer
- `created_at`: Timestamp (optional)

The `chat_title` column is parsed to extract:
- `user_id`: Unique user identifier
- `pharmacy_id` (company): Pharmacy identifier
- `demand_id`: Demand/request identifier

## Answer Quality Logic

The script uses a **simplified, grade_selector-based approach** for determining answer quality:

### Quality Categories (based on grade_selector)
- **Correct**: grade_selector = "Correct answer"
- **Incorrect**: grade_selector = "Incorrect answer"
- **Partial**: grade_selector = "Partial answer"
- **No Answer**: grade_selector = "No answer"
- **Excluded**: grade_selector = "Excluded answer"
- **Unknown**: grade_selector is empty/null or other values

### Separate No-Answer-Generated Tracking
- **no_answer_generated**: Boolean flag for responses starting with "i-do-not-even-know-what-to-answer-to-this"
- This is tracked **separately** from quality assessment
- Allows filtering to exclude/include these responses independently
- 73.7% of responses fall into this category

## Output

### Static Mode
- `overview_dashboard.png`: Main dashboard with key metrics
- `pharmacy_analysis.png`: Detailed pharmacy analysis charts
- CSV files with analysis results (if --export-csv is used)

### Interactive Dashboard
- Summary statistics with key metrics including answer quality and WAP percentages
- Pharmacy analysis with interactive charts and tables
- User analysis with distribution plots
- **Answer Quality Analysis**: Quality distribution, pharmacy performance, grade selector validation
- **Tags Analysis**: WAP vs Non-WAP patterns, tag distribution, performance by tags
- **Enhanced Sample Data Explorer**:
  - 4-dimensional filtering (pharmacy, user, quality, tags)
  - 6 sorting options including chronological (newest first by default)
  - Configurable display options (metadata, answer length, sample size)
  - Active filter indicators and sample count display
- **Performance Optimized**: Streamlit caching for instant response to UI changes
- Multi-pharmacy user analysis

## Example Results

Based on the sample data (WAP_Assistance_PROD_chats_2025-7-22.csv):

### Basic Statistics
- **6,441 total questions** from **706 unique pharmacies** and **1,703 unique users**
- **Average 9.1 questions per pharmacy** and **3.8 questions per user**
- Top pharmacy: **85 questions from 10 users**
- Most active user: **44 questions**

### Answer Quality Insights (Updated Logic)
- **Overall correct answer rate: 88.7%** (based purely on grade_selector)
- **5,712 correct answers** (88.7%)
- **272 incorrect answers** (4.2%)
- **63 partial answers** (1.0%)
- **205 no answers** (3.2%)
- **98 unknown answers** (1.5%)
- **4,748 no answer generated** (73.7%) - "i-do-not-even-know-what-to-answer-to-this" responses (tracked separately)

### Tags Distribution
- **WAP questions: 1,506** (23.4%)
- **Not WAP questions: 4,812** (74.7%)
- **No tag questions: 123** (1.9%)

### Key Findings
- **Simplified Quality Logic**: Answer quality now based purely on grade_selector (88.7% marked as "Correct")
- **Separate No-Answer Tracking**: 73.7% of responses contain "i-do-not-even-know-what-to-answer-to-this" pattern
- **Enhanced Filtering**: Can now exclude or focus specifically on no-answer-generated responses
- WAP-related questions represent about 1/4 of all inquiries
- Answer quality varies significantly by pharmacy and tag category

## Dependencies

### Required (already in requirements.txt)
- pandas
- matplotlib  
- plotly

### Optional (for enhanced functionality)
- streamlit (for interactive dashboard)
- seaborn (for enhanced static visualizations)

## Installation of Optional Dependencies

```bash
pip install streamlit seaborn
```

Note: These are not added to the main requirements.txt as this is an analysis script, not core application functionality.
