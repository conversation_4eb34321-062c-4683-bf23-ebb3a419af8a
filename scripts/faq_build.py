import argparse
import asyncio
import json
import logging
import os
from typing import List, Dict, Any

import hdbscan
import numpy as np
import pandas as pd
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.documents import Document
from langchain_core.prompts import PromptTemplate
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from sklearn.cluster import AgglomerativeClustering, KMeans
from sklearn.metrics.pairwise import cosine_distances


def setup_logging():
    """Set up logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    # Set debug level if DEBUG env var is set
    if os.getenv('DEBUG') == 'true':
        logging.getLogger().setLevel(logging.DEBUG)


logger = logging.getLogger(__name__)


def load_data(path: str) -> pd.DataFrame:
    """
    Load FAQ data from CSV or other tabular format.
    Expected columns: 'question', 'answer', 'created_at', 'rating', 'answer_relevancy'
    """
    logger.info(f"Loading data from {path}")

    # Use more robust CSV parsing options to handle complex text with quotes and newlines
    try:
        # First attempt with standard options
        df = pd.read_csv(path, parse_dates=['created_at'])
    except Exception as e:
        logger.warning(f"Standard CSV parsing failed: {e}")
        logger.info("Trying with more robust parsing options...")

        # Try with more robust options for complex CSV files
        df = pd.read_csv(
            path,
            parse_dates=['created_at'],
            quoting=1,  # QUOTE_ALL
            escapechar='\\',
            on_bad_lines='warn'
        )

    logger.info(f"Loaded {len(df)} records")

    # Validate required columns
    required_columns = ['question', 'answer']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")

    # Check for NaN values in required columns
    for col in required_columns:
        nan_count = df[col].isna().sum()
        if nan_count > 0:
            logger.warning(f"Found {nan_count} NaN values in required column '{col}'")

    # Log optional columns status
    optional_columns = ['created_at', 'rating', 'answer_relevancy']
    for col in optional_columns:
        if col not in df.columns:
            logger.warning(f"Optional column '{col}' not found in data")

    return df


def compute_embeddings(questions: List[str]) -> np.ndarray:
    """
    Compute OpenAI embeddings for a list of questions.
    Expects OPENAI_API_KEY to be set in environment.
    """
    logger.info(f"Computing embeddings for {len(questions)} questions")

    # Filter out non-string values and convert any remaining non-string values to strings
    valid_questions = []
    for q in questions:
        if pd.isna(q):
            logger.warning(f"Skipping NaN question")
            continue
        if not isinstance(q, str):
            logger.warning(f"Converting non-string question to string: {type(q)}")
            q = str(q)
        valid_questions.append(q)

    if len(valid_questions) < len(questions):
        logger.warning(f"Filtered out {len(questions) - len(valid_questions)} invalid questions")

    if not valid_questions:
        logger.error("No valid questions to embed")
        return np.array([])

    embedder = OpenAIEmbeddings()
    embeddings = embedder.embed_documents(valid_questions)
    logger.info(f"Successfully computed embeddings with shape: {np.array(embeddings).shape}")
    return np.array(embeddings)


def cluster_embeddings(
    embeddings: np.ndarray,
    method: str = 'hdbscan',
    expected_clusters: int = None,
    **kwargs
) -> np.ndarray:
    """
    Cluster embeddings using specified method with optimized defaults for FAQ data.

    Args:
        embeddings: Embedding vectors to cluster (should be normalized, like OpenAI embeddings)
        method: 'hdbscan', 'agglomerative', or 'kmeans'
        expected_clusters: Expected number of clusters (helps set better defaults)
        **kwargs: Additional parameters for the clustering algorithm

    Note:
        For HDBSCAN, cosine distance is always used (optimal for OpenAI embeddings).
        The metric parameter is ignored for HDBSCAN as it uses precomputed cosine distances.
    """
    n_samples = len(embeddings)
    logger.info(f"Clustering {n_samples} embeddings using {method}")

    # Estimate expected clusters if not provided
    if expected_clusters is None:
        # For FAQ data, assume 5-10 questions per cluster on average
        expected_clusters = max(10, n_samples // 7)

    logger.info(f"Expected clusters: {expected_clusters}")

    if method == 'hdbscan':
        # Optimized defaults for FAQ clustering with cosine distance
        if 'min_cluster_size' not in kwargs:
            # For ~1000 samples expecting ~150 clusters: min_cluster_size should be small
            kwargs['min_cluster_size'] = max(2, min(5, n_samples // (expected_clusters * 2)))
        if 'min_samples' not in kwargs:
            kwargs['min_samples'] = max(1, kwargs['min_cluster_size'] // 2)
        if 'cluster_selection_epsilon' not in kwargs:
            # Help merge very close clusters
            kwargs['cluster_selection_epsilon'] = 0.1

        # Force cosine distance for OpenAI embeddings by using precomputed distance matrix
        logger.info("Computing cosine distance matrix for HDBSCAN (optimal for OpenAI embeddings)")
        cosine_dist_matrix = cosine_distances(embeddings)

        # Set metric to precomputed and remove any user-specified metric
        kwargs['metric'] = 'precomputed'

        logger.info(f"HDBSCAN parameters: {kwargs}")
        logger.info(f"Using cosine distance matrix of shape: {cosine_dist_matrix.shape}")

        clusterer = hdbscan.HDBSCAN(**kwargs)
        labels = clusterer.fit_predict(cosine_dist_matrix)

    elif method == 'agglomerative':
        # Optimized defaults for FAQ clustering
        if 'n_clusters' not in kwargs:
            kwargs['n_clusters'] = expected_clusters
        if 'metric' not in kwargs:
            # For AgglomerativeClustering, cosine is supported
            kwargs['metric'] = 'cosine'
        if 'linkage' not in kwargs:
            # Average linkage works better with cosine distance
            kwargs['linkage'] = 'average'

        logger.info(f"AgglomerativeClustering parameters: {kwargs}")
        clusterer = AgglomerativeClustering(**kwargs)
        labels = clusterer.fit_predict(embeddings)

    elif method == 'kmeans':
        # K-means as an alternative option
        if 'n_clusters' not in kwargs:
            kwargs['n_clusters'] = expected_clusters
        if 'n_init' not in kwargs:
            kwargs['n_init'] = 10

        logger.info(f"KMeans parameters: {kwargs}")
        clusterer = KMeans(**kwargs)
        labels = clusterer.fit_predict(embeddings)

    else:
        raise ValueError(f"Unknown clustering method: {method}. Supported: 'hdbscan', 'agglomerative', 'kmeans'")

    # Log clustering results with quality metrics
    unique_labels = np.unique(labels)
    n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)  # Exclude noise
    n_noise = np.sum(labels == -1) if -1 in unique_labels else 0

    # Calculate cluster size statistics
    if n_clusters > 0:
        cluster_sizes = [np.sum(labels == label) for label in unique_labels if label != -1]
        avg_cluster_size = np.mean(cluster_sizes)
        min_cluster_size = np.min(cluster_sizes)
        max_cluster_size = np.max(cluster_sizes)

        logger.info(f"Clustering results: {n_clusters} clusters, {n_noise} noise points")
        logger.info(f"Cluster size stats - avg: {avg_cluster_size:.1f}, min: {min_cluster_size}, max: {max_cluster_size}")

        # Quality warnings
        if n_clusters < expected_clusters * 0.5:
            logger.warning(f"Too few clusters ({n_clusters}) compared to expected ({expected_clusters}). Consider reducing min_cluster_size or cluster_selection_epsilon.")
        elif n_clusters > expected_clusters * 2:
            logger.warning(f"Too many clusters ({n_clusters}) compared to expected ({expected_clusters}). Consider increasing min_cluster_size or cluster_selection_epsilon.")
    else:
        logger.warning("No clusters found! All points classified as noise.")

    return labels


async def summarize_cluster(
    cluster_id: int,
    questions: List[str],
    answers: List[str],
    llm_model: str = 'gpt-4.1',
    temperature: float = 0.5,
    semaphore: asyncio.Semaphore = None
) -> Dict[str, Any]:
    """
    Async version of summarize_cluster for concurrent processing.
    Uses semaphore to limit concurrent LLM calls.

    Generates FAQ-style canonical questions and consolidated answers.
    For answer consolidation, includes the canonical question in the prompt
    to ensure the answer directly addresses the FAQ question.

    The generated answers are formatted specifically for FAQ use, excluding
    chatbot-style elements like "feel free to ask", summary sections, and
    conversational language.

    Returns dict with cluster_id, canonical_question, consolidated_answer, source_count
    """
    async with semaphore:
        logger.info(f"Summarizing cluster {cluster_id} with {len(questions)} questions and {len(answers)} answers")

        # Handle edge cases
        if not questions or not answers:
            logger.warning(f"Empty questions or answers list provided for cluster {cluster_id}")
            return {
                'cluster_id': cluster_id,
                'canonical_question': "",
                'consolidated_answer': "",
                'source_count': 0,
                'questions': questions,
                'answers': answers
            }

        if len(questions) == 1 and len(answers) == 1:
            logger.info(f"Single question/answer pair for cluster {cluster_id}, applying FAQ formatting")

            # Initialize LLM for single item formatting
            llm = ChatOpenAI(model=llm_model, temperature=temperature)

            # Format single question as FAQ question
            question_prompt = PromptTemplate.from_template(
                "You are creating a FAQ (Frequently Asked Questions) entry. Rewrite this user question to be a clear, professional FAQ question that would be appropriate for a FAQ section.\n\n"
                "The question should:\n"
                "- Be professionally worded and clear\n"
                "- Be phrased as a typical FAQ question that users would expect to find\n"
                "- Maintain the original intent and meaning\n\n"
                "User question: {question}\n\n"
                "Generate a professional FAQ question:"
            )

            canonical_q = await llm.ainvoke(question_prompt.format(question=questions[0]))

            # Format single answer as FAQ answer
            answer_prompt = PromptTemplate.from_template(
                "You are creating a FAQ (Frequently Asked Questions) entry. Rewrite this answer to be a comprehensive, professional FAQ answer for the following question.\n\n"
                "FAQ Question: {canonical_question}\n\n"
                "Your answer should:\n"
                "- Directly address the FAQ question above\n"
                "- Be written in a professional, informational FAQ style\n"
                "- Be clear, concise, and actionable\n"
                "- Be structured and easy to read\n"
                "- Focus on providing information, not conversation\n\n"
                "IMPORTANT: Do NOT include:\n"
                "- Summary sections\n"
                "- Phrases like 'feel free to ask', 'don't hesitate to contact', 'further assistance'\n"
                "- Conversational elements or chatbot-style language\n"
                "- Invitations for follow-up questions\n"
                "- Any concluding statements about getting help\n\n"
                "Original answer: {answer}\n\n"
                "Generate a direct, informational FAQ answer:"
            )

            consolidated_a = await llm.ainvoke(answer_prompt.format(
                canonical_question=canonical_q.content.strip(),
                answer=answers[0]
            ))

            return {
                'cluster_id': cluster_id,
                'canonical_question': canonical_q.content.strip(),
                'consolidated_answer': consolidated_a.content.strip(),
                'source_count': 1,
                'questions': questions,
                'answers': answers
            }

        # Initialize LLM
        llm = ChatOpenAI(model=llm_model, temperature=temperature)

        # Prepare documents for questions (ensure all are strings)
        question_docs = [Document(page_content=str(q)) for q in questions if pd.notna(q)]

        # Create prompt for question summarization with FAQ context
        question_prompt = PromptTemplate.from_template(
            "You are creating a FAQ (Frequently Asked Questions) entry. Your task is to analyze these similar user questions and create a single, clear, canonical question that would be appropriate for a FAQ section.\n\n"
            "The canonical question should:\n"
            "- Be professionally worded and clear\n"
            "- Capture the common intent of all the questions\n"
            "- Be phrased as a typical FAQ question that users would expect to find\n"
            "- Be concise but comprehensive\n\n"
            "Similar user questions:\n"
            "{context}\n\n"
            "Generate a canonical FAQ question:"
        )

        # Create chain for question summarization
        question_chain = create_stuff_documents_chain(llm, question_prompt)

        # Run question summarization using async ainvoke
        canonical_q = await question_chain.ainvoke({"context": question_docs})

        # Prepare documents for answers (ensure all are strings)
        answer_docs = [Document(page_content=str(a)) for a in answers if pd.notna(a)]

        # Create prompt for answer consolidation with canonical question context
        answer_prompt = PromptTemplate.from_template(
            "You are creating a FAQ (Frequently Asked Questions) entry. Your task is to write a comprehensive answer for the following FAQ question by consolidating the provided source answers.\n\n"
            "FAQ Question: {canonical_question}\n\n"
            "Your answer should:\n"
            "- Directly address the FAQ question above\n"
            "- Be written in a professional, informational FAQ style\n"
            "- Consolidate all relevant information from the source answers\n"
            "- Remove redundancy while preserving important details\n"
            "- Be clear, concise, and actionable\n"
            "- Be structured and easy to read\n"
            "- Focus on providing information, not conversation\n\n"
            "IMPORTANT: Do NOT include:\n"
            "- Summary sections\n"
            "- Phrases like 'feel free to ask', 'don't hesitate to contact', 'further assistance'\n"
            "- Conversational elements or chatbot-style language\n"
            "- Invitations for follow-up questions\n"
            "- Any concluding statements about getting help\n\n"
            "Source answers to consolidate:\n"
            "{context}\n\n"
            "Generate a direct, informational FAQ answer:"
        )

        # Create chain for answer consolidation
        answer_chain = create_stuff_documents_chain(llm, answer_prompt)

        # Run answer consolidation using async ainvoke with canonical question
        consolidated_a = await answer_chain.ainvoke({
            "context": answer_docs,
            "canonical_question": canonical_q.strip()
        })

        logger.info(f"Successfully summarized cluster {cluster_id}")
        return {
            'cluster_id': cluster_id,
            'canonical_question': canonical_q.strip(),
            'consolidated_answer': consolidated_a.strip(),
            'source_count': len(questions),
            'questions': questions,
            'answers': answers
        }


async def build_faq(
    df: pd.DataFrame,
    embed_method: str = 'openai',
    cluster_method: str = 'hdbscan',
    cluster_kwargs: Dict[str, Any] = None,
    llm_model: str = 'gpt-4.1',
    expected_clusters: int = None,
    max_workers: int = 8,
) -> pd.DataFrame:
    """
    Async version of build_faq with concurrent cluster summarization.

    Args:
        df: Input DataFrame with columns 'question', 'answer', 'created_at', 'rating', 'answer_relevancy'
        embed_method: 'openai' (default) or 'local' (using sentence-transformers)
        cluster_method: 'hdbscan', 'agglomerative', or 'kmeans'
        cluster_kwargs: Additional parameters for the clustering algorithm
        llm_model: OpenAI model for summarization
        expected_clusters: Expected number of clusters (helps optimize clustering parameters)
        max_workers: Maximum number of concurrent LLM calls for cluster summarization

    Returns a DataFrame with columns:
      'cluster_id', 'canonical_question', 'consolidated_answer', 'source_count'
    """
    logger.info("Starting async FAQ building pipeline")

    # Validate input data
    if df.empty:
        logger.warning("Empty DataFrame provided")
        return pd.DataFrame(columns=['cluster_id', 'canonical_question', 'consolidated_answer', 'source_count'])

    # Filter out rows with invalid questions before processing
    original_len = len(df)
    df_clean = df.dropna(subset=['question']).copy()
    df_clean = df_clean[df_clean['question'].astype(str).str.strip() != '']

    if len(df_clean) < original_len:
        logger.warning(f"Filtered out {original_len - len(df_clean)} rows with invalid questions")

    if df_clean.empty:
        logger.error("No valid questions remaining after filtering")
        return pd.DataFrame(columns=['cluster_id', 'canonical_question', 'consolidated_answer', 'source_count'])

    # 1. Compute embeddings (synchronous)
    questions = df_clean['question'].astype(str).tolist()
    embeddings = compute_embeddings(questions)

    # 2. Cluster (synchronous)
    cluster_kwargs = cluster_kwargs or {}
    labels = cluster_embeddings(
        embeddings,
        method=cluster_method,
        expected_clusters=expected_clusters,
        **cluster_kwargs
    )
    df_clean['cluster_id'] = labels

    # 3. Prepare cluster data for async processing
    unique_labels = sorted(set(labels))
    valid_clusters = [label for label in unique_labels if label >= 0]  # Skip noise clusters

    if not valid_clusters:
        logger.warning("No valid clusters found")
        return pd.DataFrame(columns=['cluster_id', 'canonical_question', 'consolidated_answer', 'source_count'])

    logger.info(f"Processing {len(valid_clusters)} clusters with up to {max_workers} concurrent workers")

    # Create semaphore to limit concurrent LLM calls
    semaphore = asyncio.Semaphore(max_workers)

    # Prepare tasks for async processing
    tasks = []
    for cluster_id in valid_clusters:
        sub = df_clean[df_clean['cluster_id'] == cluster_id]

        # Sort by available quality metrics (handle missing columns gracefully)
        sort_columns = []
        sort_ascending = []

        for col, ascending in [('rating', False), ('answer_relevancy', False), ('created_at', False)]:
            if col in sub.columns:
                sort_columns.append(col)
                sort_ascending.append(ascending)

        if sort_columns:
            sub = sub.sort_values(by=sort_columns, ascending=sort_ascending)

        qs = sub['question'].tolist()
        ans = sub['answer'].tolist()

        # Create async task for this cluster
        task = summarize_cluster(
            cluster_id=cluster_id,
            questions=qs,
            answers=ans,
            llm_model=llm_model,
            temperature=0.5,
            semaphore=semaphore
        )
        tasks.append(task)

    # 4. Execute all cluster summarization tasks concurrently
    logger.info(f"Starting concurrent processing of {len(tasks)} clusters...")
    start_time = asyncio.get_event_loop().time()

    results = await asyncio.gather(*tasks)

    end_time = asyncio.get_event_loop().time()
    processing_time = end_time - start_time

    logger.info(f"Completed async processing in {processing_time:.2f} seconds")
    logger.info(f"Generated {len(results)} FAQ entries from {len(valid_clusters)} clusters")

    # Filter out any empty results
    valid_results = [r for r in results if r['canonical_question'] and r['consolidated_answer']]

    return pd.DataFrame(valid_results)


async def main():
    """Main async function that handles command line arguments and runs the FAQ building process."""
    # Setup logging first
    setup_logging()

    parser = argparse.ArgumentParser(description="Generate FAQ from Q&A DataFrame.")
    parser.add_argument('--input', required=True, help='Path to input CSV file')
    parser.add_argument('--output', required=True, help='Path to output CSV file')
    parser.add_argument('--api_key', help='OpenAI API key')
    parser.add_argument('--cluster_method', choices=['hdbscan', 'agglomerative', 'kmeans'], default='hdbscan',
                        help='Clustering method to use (HDBSCAN always uses cosine distance, optimal for OpenAI embeddings)')
    parser.add_argument('--cluster_kwargs', default='{}',
                        help='JSON string of clustering parameters (e.g., \'{"min_cluster_size": 5}\')')
    parser.add_argument('--expected_clusters', type=int, default=None,
                        help='Expected number of clusters (helps optimize clustering parameters)')
    parser.add_argument('--llm_model', default='gpt-4.1',
                        help='OpenAI model version for summarization')
    parser.add_argument('--max_workers', type=int, default=8,
                        help='Maximum number of concurrent LLM calls for cluster summarization (default: 8)')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose logging')

    args = parser.parse_args()

    # Set logging level based on verbose flag
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    logger.info("Starting FAQ building process")
    logger.info(f"Input file: {args.input}")
    logger.info(f"Output file: {args.output}")
    logger.info(f"Clustering method: {args.cluster_method}")
    logger.info(f"LLM model: {args.llm_model}")
    logger.info(f"Max workers: {args.max_workers}")
    logger.info("Using async processing with concurrent cluster summarization")

    if args.api_key:
        # Set up OpenAI API key globally
        original_api_key = os.environ.get('OPENAI_API_KEY')
        os.environ['OPENAI_API_KEY'] = args.api_key
    else:
        original_api_key = None

    try:
        df = load_data(args.input)

        cluster_kwargs = json.loads(args.cluster_kwargs)
        logger.info(f"Cluster parameters: {cluster_kwargs}")

        # Preprocess and filter data (only if these columns exist)
        count = len(df)
        if 'answer' in df.columns:
            df = df[df['answer'] != 'i-do-not-even-know-what-to-answer-to-this-44']
        if 'grade_selector' in df.columns:
            df = df[df['grade_selector'] == 'Correct answer']

        logger.info(f"Filtered {count - len(df)} records")

        faq_df = await build_faq(
            df,
            cluster_method=args.cluster_method,
            cluster_kwargs=cluster_kwargs,
            llm_model=args.llm_model,
            expected_clusters=args.expected_clusters,
            max_workers=args.max_workers
        )

        faq_df.to_csv(args.output, index=False)

        logger.info(f"FAQ with {len(faq_df)} entries written to {args.output}")
        print(f"Successfully generated FAQ with {len(faq_df)} entries")
        print(f"Output saved to: {args.output}")

    except Exception as e:
        logger.error(f"FAQ building failed: {e}")
        print(f"Error: {e}")
        raise SystemExit(1)
    finally:
        # Restore original API key or remove if it wasn't set
        if original_api_key is not None:
            os.environ['OPENAI_API_KEY'] = original_api_key
        else:
            os.environ.pop('OPENAI_API_KEY', None)


if __name__ == '__main__':
    asyncio.run(main())
