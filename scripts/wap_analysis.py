"""
WAP Chat Analysis Script

This script analyzes WAP chat data by pharmacy and user, providing insights into:
- Number of pharmacies and users involved
- Question distribution by pharmacy and user
- Sample questions from different groups

Usage:
    # Static mode (matplotlib charts)
    python scripts/wap_analysis.py -f data.csv --mode static --export-csv

    # Interactive dashboard (requires streamlit)
    pip install streamlit seaborn  # Optional dependencies
    streamlit run scripts/wap_analysis.py -- -f data.csv

Required dependencies: pandas, matplotlib, plotly (already in requirements.txt)
Optional dependencies: streamlit, seaborn (for enhanced functionality)
"""

import argparse
import pandas as pd
import matplotlib.pyplot as plt
import re
from typing import Tuple, Dict, Any
import plotly.express as px
import json

# Optional dependencies for enhanced functionality
import seaborn as sns

import streamlit as st

# Configure Streamlit page (must be first Streamlit command)
st.set_page_config(page_title="WAP Chat Analysis Dashboard", layout="wide")


def parse_chat_title(chat_title: str) -> Tuple[str, str, str]:
    """
    Parse chat_title to extract user_id, company_id (pharmacy), and demand_id.

    Args:
        chat_title: String in format "user:358794_company:57377_demand:211833"

    Returns:
        Tuple of (user_id, company_id, demand_id)
    """
    try:
        # Use regex to extract the IDs
        pattern = r'user:(\d+)_company:(\d+)_demand:(\d+)'
        match = re.match(pattern, chat_title)

        if match:
            user_id, company_id, demand_id = match.groups()
            return user_id, company_id, demand_id
        else:
            # Fallback for any unexpected format
            return "unknown", "unknown", "unknown"
    except Exception as e:
        print(f"Error parsing chat_title '{chat_title}': {e}")
        return "unknown", "unknown", "unknown"


def parse_context_json(context_str: str) -> Tuple[str, str, str]:
    """
    Parse context JSON to extract topic_group, topic, and not_answer_reason.

    Args:
        context_str: JSON string containing context information

    Returns:
        Tuple of (topic_group, topic, not_answer_reason)
    """
    try:
        if pd.isna(context_str) or context_str == '':
            return 'Unknown', 'Unknown', ''

        context_data = json.loads(context_str)
        topic_group = context_data.get('topic_group', 'Unknown')
        topic = context_data.get('topic', 'Unknown')
        not_answer_reason = context_data.get('not_answer_reason', '')

        return topic_group, topic, not_answer_reason
    except (json.JSONDecodeError, TypeError, AttributeError):
        return 'Unknown', 'Unknown', ''


@st.cache_data
def load_and_process_data(file_path: str) -> pd.DataFrame:
    """
    Load CSV data and add parsed user_id and pharmacy_id columns.

    Args:
        file_path: Path to the CSV file

    Returns:
        Processed DataFrame with additional columns
    """
    print(f"Loading data from {file_path}...")
    df = pd.read_csv(file_path)

    print(f"Loaded {len(df)} rows")
    print(f"Columns: {list(df.columns)}")

    # Parse chat_title to extract IDs
    print("Parsing chat titles...")
    parsed_data = df['chat_title'].apply(parse_chat_title)
    df[['user_id', 'pharmacy_id', 'demand_id']] = pd.DataFrame(parsed_data.tolist(), index=df.index)

    # Convert to numeric for better analysis
    df['user_id_num'] = pd.to_numeric(df['user_id'], errors='coerce')
    df['pharmacy_id_num'] = pd.to_numeric(df['pharmacy_id'], errors='coerce')
    df['demand_id_num'] = pd.to_numeric(df['demand_id'], errors='coerce')

    # Process grade_selector and answer quality
    print("Processing answer quality...")
    df = process_answer_quality(df)

    # Process tags
    print("Processing tags...")
    df = process_tags(df)

    # Process context (categories)
    print("Processing categories...")
    if 'context' in df.columns:
        parsed_context = df['context'].apply(parse_context_json)
        df[['topic_group', 'topic', 'not_answer_reason']] = pd.DataFrame(parsed_context.tolist(), index=df.index)
    else:
        print("Warning: 'context' column not found, setting default category values")
        df['topic_group'] = 'Unknown'
        df['topic'] = 'Unknown'
        df['not_answer_reason'] = ''

    # Set categories to "Excluded" for questions that should be excluded from category analysis
    # Condition 1: Answer quality is "Excluded"
    excluded_quality_mask = df['answer_quality'] == 'Excluded'

    # Condition 2: not_answer_reason is "not_chat_topics"
    excluded_reason_mask = df['not_answer_reason'] == 'not_chat_topics'

    # Combined exclusion mask (either condition)
    excluded_mask = excluded_quality_mask | excluded_reason_mask

    if excluded_mask.any():
        excluded_count = excluded_mask.sum()
        quality_count = excluded_quality_mask.sum()
        reason_count = excluded_reason_mask.sum()
        overlap_count = (excluded_quality_mask & excluded_reason_mask).sum()

        print(f"Setting categories to 'Excluded' for {excluded_count} questions:")
        print(f"  - {quality_count} with excluded answer quality")
        print(f"  - {reason_count} with not_answer_reason='not_chat_topics'")
        if overlap_count > 0:
            print(f"  - {overlap_count} questions match both conditions")

        df.loc[excluded_mask, 'topic_group'] = 'Excluded'
        df.loc[excluded_mask, 'topic'] = 'Excluded'

    # Remove rows with unknown IDs
    initial_count = len(df)
    df = df[df['user_id'] != 'unknown']
    final_count = len(df)

    if initial_count != final_count:
        print(f"Removed {initial_count - final_count} rows with unparseable chat_titles")

    return df


def process_answer_quality(df: pd.DataFrame) -> pd.DataFrame:
    """
    Process grade_selector and answer columns to determine answer quality.

    Args:
        df: DataFrame with grade_selector and answer columns

    Returns:
        DataFrame with additional answer quality columns
    """
    # Create a copy to avoid modifying the original
    df = df.copy()

    # Fill NaN values in grade_selector and answer
    df['grade_selector'] = df['grade_selector'].fillna('Unknown')
    df['answer'] = df['answer'].fillna('')

    # Check for "i-do-not-even-know-what-to-answer-to-this" pattern - mark as no-answer-generated
    df['no_answer_generated'] = df['answer'].str.startswith('i-do-not-even-know-what-to-answer-to-this', na=False)

    # Create answer quality based purely on grade_selector
    def determine_answer_quality(row):
        if row['grade_selector'] == 'Correct answer':
            return 'Correct'
        elif row['grade_selector'] == 'Incorrect answer':
            return 'Incorrect'
        elif row['grade_selector'] == 'Partial answer':
            return 'Partial'
        elif row['grade_selector'] == 'No answer':
            return 'No Answer'
        elif row['grade_selector'] == 'Excluded answer':
            return 'Excluded'
        else:
            return 'Unknown'

    df['answer_quality'] = df.apply(determine_answer_quality, axis=1)

    return df


def process_tags(df: pd.DataFrame) -> pd.DataFrame:
    """
    Process tags column to standardize and analyze tag distribution.

    Args:
        df: DataFrame with tags column

    Returns:
        DataFrame with processed tags
    """
    # Create a copy to avoid modifying the original
    df = df.copy()

    # Fill NaN values in tags
    df['tags'] = df['tags'].fillna('No Tag')

    # Standardize tag values
    df['tags_clean'] = df['tags'].str.strip()

    # Create binary columns for main tag categories
    df['is_wap'] = df['tags_clean'] == 'WAP'
    df['is_not_wap'] = df['tags_clean'] == 'Not WAP'
    df['has_no_tag'] = df['tags_clean'] == 'No Tag'

    return df


@st.cache_data
def generate_summary_stats(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Generate summary statistics for the dataset.

    Args:
        df: Processed DataFrame

    Returns:
        Dictionary with summary statistics
    """
    stats = {
        'total_questions': len(df),
        'unique_pharmacies': df['pharmacy_id'].nunique(),
        'unique_users': df['user_id'].nunique(),
        'unique_demands': df['demand_id'].nunique(),
        'avg_questions_per_pharmacy': len(df) / df['pharmacy_id'].nunique(),
        'avg_questions_per_user': len(df) / df['user_id'].nunique(),
        'date_range': {
            'start': df['created_at'].min() if 'created_at' in df.columns else 'N/A',
            'end': df['created_at'].max() if 'created_at' in df.columns else 'N/A'
        }
    }

    return stats


@st.cache_data
def create_pharmacy_analysis(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze data by pharmacy.

    Args:
        df: Processed DataFrame

    Returns:
        Dictionary with pharmacy analysis results
    """
    # Questions per pharmacy
    pharmacy_questions = df.groupby('pharmacy_id').size().reset_index(name='question_count')
    pharmacy_questions = pharmacy_questions.sort_values('question_count', ascending=False)

    # Users per pharmacy
    pharmacy_users = df.groupby('pharmacy_id')['user_id'].nunique().reset_index(name='user_count')

    # Merge the data
    pharmacy_stats = pharmacy_questions.merge(pharmacy_users, on='pharmacy_id')
    pharmacy_stats['avg_questions_per_user'] = pharmacy_stats['question_count'] / pharmacy_stats['user_count']

    return {
        'pharmacy_stats': pharmacy_stats,
        'top_10_pharmacies': pharmacy_stats.head(10),
        'question_distribution': pharmacy_questions['question_count'].describe()
    }


@st.cache_data
def create_user_analysis(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze data by user.

    Args:
        df: Processed DataFrame

    Returns:
        Dictionary with user analysis results
    """
    # Questions per user
    user_questions = df.groupby('user_id').size().reset_index(name='question_count')
    user_questions = user_questions.sort_values('question_count', ascending=False)

    # Users per pharmacy
    user_pharmacy = df.groupby('user_id')['pharmacy_id'].nunique().reset_index(name='pharmacy_count')

    # Merge the data
    user_stats = user_questions.merge(user_pharmacy, on='user_id')

    return {
        'user_stats': user_stats,
        'top_10_users': user_stats.head(10),
        'question_distribution': user_questions['question_count'].describe(),
        'multi_pharmacy_users': user_stats[user_stats['pharmacy_count'] > 1]
    }


@st.cache_data
def create_answer_quality_analysis(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze answer quality based on grade_selector and answer content.

    Args:
        df: Processed DataFrame with answer_quality column

    Returns:
        Dictionary with answer quality analysis results
    """
    # Overall answer quality distribution
    quality_dist = df['answer_quality'].value_counts().reset_index()
    quality_dist.columns = ['answer_quality', 'count']
    quality_dist['percentage'] = (quality_dist['count'] / len(df) * 100).round(2)

    # Answer quality by pharmacy
    pharmacy_quality = df.groupby(['pharmacy_id', 'answer_quality']).size().unstack(fill_value=0)
    pharmacy_quality['total'] = pharmacy_quality.sum(axis=1)
    pharmacy_quality['correct_rate'] = (
                pharmacy_quality.get('Correct', 0) / pharmacy_quality['total'] * 100).round(2)
    pharmacy_quality = pharmacy_quality.reset_index().sort_values('correct_rate', ascending=False)

    # Answer quality by tags
    tag_quality = df.groupby(['tags_clean', 'answer_quality']).size().unstack(fill_value=0)
    tag_quality['total'] = tag_quality.sum(axis=1)
    if 'Correct' in tag_quality.columns:
        tag_quality['correct_rate'] = (tag_quality['Correct'] / tag_quality['total'] * 100).round(2)
    else:
        tag_quality['correct_rate'] = 0
    tag_quality = tag_quality.reset_index()

    # Grade selector vs actual quality (considering unknown answers)
    grade_vs_quality = pd.crosstab(df['grade_selector'], df['answer_quality'], margins=True)

    return {
        'quality_distribution': quality_dist,
        'pharmacy_quality': pharmacy_quality,
        'tag_quality': tag_quality,
        'grade_vs_quality': grade_vs_quality,
        'top_performing_pharmacies': pharmacy_quality.head(20),
        'quality_stats': {
            'total_questions': len(df),
            'correct_answers': len(df[df['answer_quality'] == 'Correct']),
            'incorrect_answers': len(df[df['answer_quality'] == 'Incorrect']),
            'partial_answers': len(df[df['answer_quality'] == 'Partial']),
            'no_answers': len(df[df['answer_quality'] == 'No Answer']),
            'excluded_answers': len(df[df['answer_quality'] == 'Excluded']),
            'unknown_answers': len(df[df['answer_quality'] == 'Unknown']),
            'no_answer_generated': len(df[df['no_answer_generated']]),
            'overall_correct_rate': round(len(df[df['answer_quality'] == 'Correct']) / len(df) * 100, 2),
            'no_answer_generated_rate': round(len(df[df['no_answer_generated']]) / len(df) * 100, 2)
        }
    }


@st.cache_data
def create_tags_analysis(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze data by tags.

    Args:
        df: Processed DataFrame with tags columns

    Returns:
        Dictionary with tags analysis results
    """
    # Tag distribution
    tag_dist = df['tags_clean'].value_counts().reset_index()
    tag_dist.columns = ['tag', 'count']
    tag_dist['percentage'] = (tag_dist['count'] / len(df) * 100).round(2)

    # Questions per pharmacy by tag
    pharmacy_tag_stats = df.groupby(['pharmacy_id', 'tags_clean']).size().unstack(fill_value=0)
    pharmacy_tag_stats['total'] = pharmacy_tag_stats.sum(axis=1)
    if 'WAP' in pharmacy_tag_stats.columns:
        pharmacy_tag_stats['wap_percentage'] = (pharmacy_tag_stats['WAP'] / pharmacy_tag_stats['total'] * 100).round(2)
    else:
        pharmacy_tag_stats['wap_percentage'] = 0
    pharmacy_tag_stats = pharmacy_tag_stats.reset_index().sort_values('wap_percentage', ascending=False)

    # User engagement by tag
    user_tag_stats = df.groupby(['user_id', 'tags_clean']).size().unstack(fill_value=0)
    user_tag_stats['total'] = user_tag_stats.sum(axis=1)
    if 'WAP' in user_tag_stats.columns:
        user_tag_stats['wap_percentage'] = (user_tag_stats['WAP'] / user_tag_stats['total'] * 100).round(2)
    else:
        user_tag_stats['wap_percentage'] = 0
    user_tag_stats = user_tag_stats.reset_index().sort_values('wap_percentage', ascending=False)

    # Tag vs answer quality cross-analysis
    tag_quality_cross = pd.crosstab(df['tags_clean'], df['answer_quality'], margins=True)

    return {
        'tag_distribution': tag_dist,
        'pharmacy_tag_stats': pharmacy_tag_stats,
        'user_tag_stats': user_tag_stats,
        'tag_quality_cross': tag_quality_cross,
        'top_wap_pharmacies': pharmacy_tag_stats.head(20),
        'top_wap_users': user_tag_stats.head(20),
        'tag_stats': {
            'total_questions': len(df),
            'wap_questions': len(df[df['is_wap']]),
            'not_wap_questions': len(df[df['is_not_wap']]),
            'no_tag_questions': len(df[df['has_no_tag']]),
            'wap_percentage': round(len(df[df['is_wap']]) / len(df) * 100, 2)
        }
    }


@st.cache_data
def create_category_analysis(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze question categories (topic_group and topic) from context data.

    Args:
        df: Processed DataFrame with category columns

    Returns:
        Dictionary containing category analysis results
    """
    # Topic group distribution
    topic_group_dist = df['topic_group'].value_counts()

    # Topic (subcategory) distribution
    topic_dist = df['topic'].value_counts()

    # Category by pharmacy
    pharmacy_category_stats = df.groupby(['pharmacy_id', 'topic_group']).size().unstack(fill_value=0)
    pharmacy_category_stats['total'] = pharmacy_category_stats.sum(axis=1)
    pharmacy_category_stats = pharmacy_category_stats.reset_index()

    # Category by user
    user_category_stats = df.groupby(['user_id', 'topic_group']).size().unstack(fill_value=0)
    user_category_stats['total'] = user_category_stats.sum(axis=1)
    user_category_stats = user_category_stats.reset_index()

    # Category vs answer quality cross-analysis
    category_quality_cross = pd.crosstab(df['topic_group'], df['answer_quality'], margins=True)

    # Topic vs answer quality cross-analysis
    topic_quality_cross = pd.crosstab(df['topic'], df['answer_quality'], margins=True)

    # Category vs tags cross-analysis
    category_tag_cross = pd.crosstab(df['topic_group'], df['tags_clean'], margins=True)

    return {
        'topic_group_distribution': topic_group_dist,
        'topic_distribution': topic_dist,
        'pharmacy_category_stats': pharmacy_category_stats,
        'user_category_stats': user_category_stats,
        'category_quality_cross': category_quality_cross,
        'topic_quality_cross': topic_quality_cross,
        'category_tag_cross': category_tag_cross,
        'category_stats': {
            'total_questions': len(df),
            'unique_topic_groups': df['topic_group'].nunique(),
            'unique_topics': df['topic'].nunique()
        }
    }


def create_static_visualizations(df: pd.DataFrame, pharmacy_analysis: Dict, user_analysis: Dict, output_dir: str):
    """
    Create static matplotlib/seaborn visualizations.

    Args:
        df: Processed DataFrame
        pharmacy_analysis: Pharmacy analysis results
        user_analysis: User analysis results
        output_dir: Directory to save charts
    """
    import os
    os.makedirs(output_dir, exist_ok=True)

    # Set style
    plt.style.use('default')
    sns.set_palette("husl")

    # 1. Overview Dashboard
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('WAP Chat Analysis Overview', fontsize=16, fontweight='bold')

    # Top pharmacies by questions
    top_pharmacies = pharmacy_analysis['top_10_pharmacies']
    axes[0, 0].bar(range(len(top_pharmacies)), top_pharmacies['question_count'])
    axes[0, 0].set_title('Top 10 Pharmacies by Question Count')
    axes[0, 0].set_xlabel('Pharmacy Rank')
    axes[0, 0].set_ylabel('Number of Questions')

    # Questions per pharmacy distribution
    axes[0, 1].hist(pharmacy_analysis['pharmacy_stats']['question_count'], bins=30, alpha=0.7)
    axes[0, 1].set_title('Distribution of Questions per Pharmacy')
    axes[0, 1].set_xlabel('Number of Questions')
    axes[0, 1].set_ylabel('Number of Pharmacies')

    # Top users by questions
    top_users = user_analysis['top_10_users']
    axes[1, 0].bar(range(len(top_users)), top_users['question_count'])
    axes[1, 0].set_title('Top 10 Users by Question Count')
    axes[1, 0].set_xlabel('User Rank')
    axes[1, 0].set_ylabel('Number of Questions')

    # Questions per user distribution
    axes[1, 1].hist(user_analysis['user_stats']['question_count'], bins=30, alpha=0.7)
    axes[1, 1].set_title('Distribution of Questions per User')
    axes[1, 1].set_xlabel('Number of Questions')
    axes[1, 1].set_ylabel('Number of Users')

    plt.tight_layout()
    plt.savefig(f'{output_dir}/overview_dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Detailed Pharmacy Analysis
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Detailed Pharmacy Analysis', fontsize=16, fontweight='bold')

    # Pharmacy stats scatter plot
    pharmacy_stats = pharmacy_analysis['pharmacy_stats']
    axes[0, 0].scatter(pharmacy_stats['user_count'], pharmacy_stats['question_count'], alpha=0.6)
    axes[0, 0].set_xlabel('Number of Users')
    axes[0, 0].set_ylabel('Number of Questions')
    axes[0, 0].set_title('Users vs Questions per Pharmacy')

    # Average questions per user by pharmacy
    axes[0, 1].hist(pharmacy_stats['avg_questions_per_user'], bins=20, alpha=0.7)
    axes[0, 1].set_xlabel('Average Questions per User')
    axes[0, 1].set_ylabel('Number of Pharmacies')
    axes[0, 1].set_title('Distribution of Avg Questions per User by Pharmacy')

    # Box plot of questions per pharmacy
    axes[1, 0].boxplot(pharmacy_stats['question_count'])
    axes[1, 0].set_ylabel('Number of Questions')
    axes[1, 0].set_title('Box Plot: Questions per Pharmacy')

    # Top 20 pharmacies detailed
    top_20 = pharmacy_stats.head(20)
    axes[1, 1].barh(range(len(top_20)), top_20['question_count'])
    axes[1, 1].set_xlabel('Number of Questions')
    axes[1, 1].set_ylabel('Pharmacy Rank')
    axes[1, 1].set_title('Top 20 Pharmacies by Question Count')
    axes[1, 1].invert_yaxis()

    plt.tight_layout()
    plt.savefig(f'{output_dir}/pharmacy_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"Static visualizations saved to {output_dir}/")


@st.cache_data
def get_all_analysis_data(file_path: str):
    """
    Load and process all analysis data. This function is cached.

    Args:
        file_path: Path to the CSV file

    Returns:
        Tuple of (df, pharmacy_analysis, user_analysis, summary_stats, answer_quality_analysis, tags_analysis, category_analysis)
    """
    # Load and process data
    df = load_and_process_data(file_path)

    # Generate all analyses
    summary_stats = generate_summary_stats(df)
    pharmacy_analysis = create_pharmacy_analysis(df)
    user_analysis = create_user_analysis(df)
    answer_quality_analysis = create_answer_quality_analysis(df)
    tags_analysis = create_tags_analysis(df)
    category_analysis = create_category_analysis(df)

    return df, pharmacy_analysis, user_analysis, summary_stats, answer_quality_analysis, tags_analysis, category_analysis


def render_summary_statistics(summary_stats: Dict[str, Any], answer_quality_analysis: Dict[str, Any], tags_analysis: Dict[str, Any]):
    st.header("📊 Summary Statistics")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Questions", f"{summary_stats['total_questions']:,}")
    with col2:
        st.metric("Unique Pharmacies", f"{summary_stats['unique_pharmacies']:,}")
    with col3:
        st.metric("Unique Users", f"{summary_stats['unique_users']:,}")
    with col4:
        st.metric("Unique Demands", f"{summary_stats['unique_demands']:,}")

    col5, col6, col7, col8 = st.columns(4)
    with col5:
        st.metric("Avg Questions/Pharmacy", f"{summary_stats['avg_questions_per_pharmacy']:.1f}")
    with col6:
        st.metric("Avg Questions/User", f"{summary_stats['avg_questions_per_user']:.1f}")
    with col7:
        st.metric("Correct Answer Rate", f"{answer_quality_analysis['quality_stats']['overall_correct_rate']:.1f}%")
    with col8:
        st.metric("WAP Questions", f"{tags_analysis['tag_stats']['wap_percentage']:.1f}%")


def render_pharmacy_analysis(pharmacy_analysis: Dict[str, Any]):
    st.header("🏪 Pharmacy Analysis")

    tab1, tab2, tab3 = st.tabs(["Overview", "Top Pharmacies", "Distribution"])

    with tab1:
        col1, col2 = st.columns(2)

        with col1:
            # Scatter plot: Users vs Questions per Pharmacy
            pharmacy_stats = pharmacy_analysis['pharmacy_stats']
            fig = px.scatter(
                pharmacy_stats,
                x='user_count',
                y='question_count',
                title='Users vs Questions per Pharmacy',
                labels={'user_count': 'Number of Users', 'question_count': 'Number of Questions'},
                hover_data=['pharmacy_id']
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Histogram: Questions per Pharmacy
            fig = px.histogram(
                pharmacy_stats,
                x='question_count',
                title='Distribution of Questions per Pharmacy',
                labels={'question_count': 'Number of Questions', 'count': 'Number of Pharmacies'}
            )
            st.plotly_chart(fig, use_container_width=True)

    with tab2:
        # Top pharmacies table and chart
        st.subheader("Top 20 Pharmacies by Question Count")

        top_pharmacies = pharmacy_analysis['pharmacy_stats'].head(20)

        col1, col2 = st.columns([1, 1])

        with col1:
            st.dataframe(
                top_pharmacies[['pharmacy_id', 'question_count', 'user_count', 'avg_questions_per_user']].round(2),
                use_container_width=True
            )

        with col2:
            fig = px.bar(
                top_pharmacies,
                x='pharmacy_id',
                y='question_count',
                title='Top 20 Pharmacies by Question Count'
            )
            fig.update_xaxes(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)

    with tab3:
        # Distribution analysis
        st.subheader("Question Distribution Analysis")

        col1, col2 = st.columns(2)

        with col1:
            # Box plot
            fig = px.box(
                pharmacy_analysis['pharmacy_stats'],
                y='question_count',
                title='Box Plot: Questions per Pharmacy'
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Statistics table
            dist_stats = pharmacy_analysis['question_distribution']
            st.write("**Distribution Statistics:**")
            stats_df = pd.DataFrame({
                'Statistic': ['Count', 'Mean', 'Std', 'Min', '25%', '50%', '75%', 'Max'],
                'Value': [
                    f"{dist_stats['count']:.0f}",
                    f"{dist_stats['mean']:.2f}",
                    f"{dist_stats['std']:.2f}",
                    f"{dist_stats['min']:.0f}",
                    f"{dist_stats['25%']:.0f}",
                    f"{dist_stats['50%']:.0f}",
                    f"{dist_stats['75%']:.0f}",
                    f"{dist_stats['max']:.0f}"
                ]
            })
            st.dataframe(stats_df, use_container_width=True)


def render_user_analysis(user_analysis: Dict[str, Any]):
    st.header("👤 User Analysis")

    tab1, tab2, tab3 = st.tabs(["Overview", "Top Users", "Multi-Pharmacy Users"])

    with tab1:
        col1, col2 = st.columns(2)

        with col1:
            # Histogram: Questions per User
            user_stats = user_analysis['user_stats']
            fig = px.histogram(
                user_stats,
                x='question_count',
                title='Distribution of Questions per User',
                labels={'question_count': 'Number of Questions', 'count': 'Number of Users'}
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Histogram: Pharmacies per User
            fig = px.histogram(
                user_stats,
                x='pharmacy_count',
                title='Distribution of Pharmacies per User',
                labels={'pharmacy_count': 'Number of Pharmacies', 'count': 'Number of Users'}
            )
            st.plotly_chart(fig, use_container_width=True)

    with tab2:
        # Top users
        st.subheader("Top 20 Users by Question Count")

        top_users = user_analysis['user_stats'].head(20)

        col1, col2 = st.columns([1, 1])

        with col1:
            st.dataframe(
                top_users[['user_id', 'question_count', 'pharmacy_count']],
                use_container_width=True
            )

        with col2:
            fig = px.bar(
                top_users,
                x='user_id',
                y='question_count',
                title='Top 20 Users by Question Count'
            )
            fig.update_xaxes(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)

    with tab3:
        # Multi-pharmacy users
        multi_pharmacy = user_analysis['multi_pharmacy_users']

        if len(multi_pharmacy) > 0:
            st.subheader(f"Users Active in Multiple Pharmacies ({len(multi_pharmacy)} users)")

            col1, col2 = st.columns([1, 1])

            with col1:
                st.dataframe(
                    multi_pharmacy.head(20)[['user_id', 'question_count', 'pharmacy_count']],
                    use_container_width=True
                )

            with col2:
                fig = px.scatter(
                    multi_pharmacy,
                    x='pharmacy_count',
                    y='question_count',
                    title='Multi-Pharmacy Users: Pharmacies vs Questions',
                    hover_data=['user_id']
                )
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No users found active in multiple pharmacies.")


def render_answer_quality_analysis(answer_quality_analysis: Dict[str, Any]):
    st.header("✅ Answer Quality Analysis")

    tab1, tab2, tab3 = st.tabs(["Quality Overview", "Pharmacy Performance", "Quality vs Tags"])

    with tab1:
        col1, col2 = st.columns(2)

        with col1:
            # Answer quality distribution pie chart
            quality_dist = answer_quality_analysis['quality_distribution']
            fig = px.pie(
                quality_dist,
                values='count',
                names='answer_quality',
                title='Answer Quality Distribution'
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Quality statistics
            quality_stats = answer_quality_analysis['quality_stats']
            st.write("**Quality Statistics:**")
            stats_df = pd.DataFrame({
                'Metric': [
                    'Total Questions',
                    'Correct Answers',
                    'Incorrect Answers',
                    'Partial Answers',
                    'No Answers',
                    'Excluded Answers',
                    'Unknown Answers',
                    'No Answer Generated',
                    'Overall Correct Rate',
                    'No Answer Generated Rate'
                ],
                'Value': [
                    f"{quality_stats['total_questions']:,}",
                    f"{quality_stats['correct_answers']:,}",
                    f"{quality_stats['incorrect_answers']:,}",
                    f"{quality_stats['partial_answers']:,}",
                    f"{quality_stats['no_answers']:,}",
                    f"{quality_stats['excluded_answers']:,}",
                    f"{quality_stats['unknown_answers']:,}",
                    f"{quality_stats['no_answer_generated']:,}",
                    f"{quality_stats['overall_correct_rate']:.1f}%",
                    f"{quality_stats['no_answer_generated_rate']:.1f}%"
                ]
            })
            st.dataframe(stats_df, use_container_width=True)

    with tab2:
        # Top performing pharmacies by correct answer rate
        st.subheader("Top 20 Pharmacies by Correct Answer Rate")

        top_pharmacies_quality = answer_quality_analysis['top_performing_pharmacies']

        col1, col2 = st.columns([1, 1])

        with col1:
            # Filter pharmacies with at least 5 questions for meaningful rates
            filtered_pharmacies = top_pharmacies_quality[top_pharmacies_quality['total'] >= 5].head(20)
            st.dataframe(
                filtered_pharmacies[['pharmacy_id', 'total', 'correct_rate']].round(2),
                use_container_width=True
            )

        with col2:
            if len(filtered_pharmacies) > 0:
                fig = px.bar(
                    filtered_pharmacies.head(15),
                    x='pharmacy_id',
                    y='correct_rate',
                    title='Top 15 Pharmacies by Correct Answer Rate (≥5 questions)',
                    labels={'correct_rate': 'Correct Answer Rate (%)', 'pharmacy_id': 'Pharmacy ID'}
                )
                fig.update_xaxes(tickangle=45)
                st.plotly_chart(fig, use_container_width=True)

    with tab3:
        # Quality vs Grade Selector cross-analysis
        st.subheader("Grade Selector vs Actual Quality")

        col1, col2 = st.columns(2)

        with col1:
            # Cross-tabulation heatmap
            grade_vs_quality = answer_quality_analysis['grade_vs_quality']
            # Remove the 'All' row and column for cleaner visualization
            if 'All' in grade_vs_quality.index:
                grade_vs_quality_clean = grade_vs_quality.drop('All', axis=0)
            if 'All' in grade_vs_quality.columns:
                grade_vs_quality_clean = grade_vs_quality_clean.drop('All', axis=1)

            fig = px.imshow(
                grade_vs_quality_clean.values,
                x=grade_vs_quality_clean.columns,
                y=grade_vs_quality_clean.index,
                title='Grade Selector vs Actual Quality Heatmap',
                color_continuous_scale='Blues'
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Tag quality analysis
            tag_quality = answer_quality_analysis['tag_quality']
            st.write("**Answer Quality by Tags:**")
            st.dataframe(
                tag_quality[['tags_clean', 'total', 'correct_rate']].round(2),
                use_container_width=True
            )


def render_tags_analysis(df: pd.DataFrame, tags_analysis: Dict[str, Any]):
    st.header("🏷️ Tags Analysis")

    tab1, tab2, tab3 = st.tabs(["Tag Distribution", "WAP vs Non-WAP", "Pharmacy & User Patterns"])

    with tab1:
        col1, col2 = st.columns(2)

        with col1:
            # Tag distribution pie chart
            tag_dist = tags_analysis['tag_distribution']
            fig = px.pie(
                tag_dist,
                values='count',
                names='tag',
                title='Tag Distribution'
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Tag statistics
            tag_stats = tags_analysis['tag_stats']
            st.write("**Tag Statistics:**")
            stats_df = pd.DataFrame({
                'Metric': [
                    'Total Questions',
                    'WAP Questions',
                    'Not WAP Questions',
                    'No Tag Questions',
                    'WAP Percentage'
                ],
                'Value': [
                    f"{tag_stats['total_questions']:,}",
                    f"{tag_stats['wap_questions']:,}",
                    f"{tag_stats['not_wap_questions']:,}",
                    f"{tag_stats['no_tag_questions']:,}",
                    f"{tag_stats['wap_percentage']:.1f}%"
                ]
            })
            st.dataframe(stats_df, use_container_width=True)

    with tab2:
        # WAP vs Non-WAP analysis
        st.subheader("WAP vs Non-WAP Question Analysis")

        col1, col2 = st.columns(2)

        with col1:
            # Tag vs Quality cross-analysis
            tag_quality_cross = tags_analysis['tag_quality_cross']
            # Remove 'All' row for cleaner visualization
            if 'All' in tag_quality_cross.index:
                tag_quality_clean = tag_quality_cross.drop('All', axis=0)
            if 'All' in tag_quality_cross.columns:
                tag_quality_clean = tag_quality_clean.drop('All', axis=1)

            fig = px.imshow(
                tag_quality_clean.values,
                x=tag_quality_clean.columns,
                y=tag_quality_clean.index,
                title='Tags vs Answer Quality Heatmap',
                color_continuous_scale='Viridis'
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # WAP vs Non-WAP comparison
            wap_comparison = []
            for tag in ['WAP', 'Not WAP', 'No Tag']:
                tag_data = df[df['tags_clean'] == tag]
                if len(tag_data) > 0:
                    correct_rate = len(tag_data[tag_data['answer_quality'] == 'Correct']) / len(tag_data) * 100
                    wap_comparison.append({
                        'Tag': tag,
                        'Questions': len(tag_data),
                        'Correct Rate (%)': round(correct_rate, 2)
                    })

            if wap_comparison:
                comparison_df = pd.DataFrame(wap_comparison)
                st.write("**Performance by Tag Category:**")
                st.dataframe(comparison_df, use_container_width=True)

                # Bar chart of correct rates
                fig = px.bar(
                    comparison_df,
                    x='Tag',
                    y='Correct Rate (%)',
                    title='Correct Answer Rate by Tag Category'
                )
                st.plotly_chart(fig, use_container_width=True)

    with tab3:
        # Pharmacy and user patterns by tags
        st.subheader("Top WAP-focused Pharmacies and Users")

        col1, col2 = st.columns(2)

        with col1:
            # Top pharmacies by WAP percentage
            top_wap_pharmacies = tags_analysis['top_wap_pharmacies']
            filtered_wap_pharmacies = top_wap_pharmacies[top_wap_pharmacies['total'] >= 5].head(15)

            st.write("**Top 15 Pharmacies by WAP Percentage (≥5 questions):**")
            st.dataframe(
                filtered_wap_pharmacies[['pharmacy_id', 'total', 'wap_percentage']].round(2),
                use_container_width=True
            )

        with col2:
            # Top users by WAP percentage
            top_wap_users = tags_analysis['top_wap_users']
            filtered_wap_users = top_wap_users[top_wap_users['total'] >= 3].head(15)

            st.write("**Top 15 Users by WAP Percentage (≥3 questions):**")
            st.dataframe(
                filtered_wap_users[['user_id', 'total', 'wap_percentage']].round(2),
                use_container_width=True
            )


def render_category_analysis(df: pd.DataFrame, category_analysis: Dict[str, Any]):
    st.header("📂 Category Analysis")

    # Category statistics overview
    category_stats = category_analysis['category_stats']
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Total Categories", category_stats['unique_topic_groups'])
    with col2:
        st.metric("Total Subcategories", category_stats['unique_topics'])
    with col3:
        st.metric("Total Questions", category_stats['total_questions'])

    tab1, tab2, tab3 = st.tabs(["Category Distribution", "Subcategory Analysis", "Category Filtering"])

    with tab1:
        col1, col2 = st.columns(2)

        with col1:
            # Topic group pie chart
            topic_group_dist = category_analysis['topic_group_distribution']
            fig = px.pie(
                values=topic_group_dist.values,
                names=topic_group_dist.index,
                title='Distribution by Category (Topic Group)'
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Category vs Quality heatmap
            category_quality_cross = category_analysis['category_quality_cross']
            if 'All' in category_quality_cross.index:
                category_quality_clean = category_quality_cross.drop('All', axis=0)
            if 'All' in category_quality_cross.columns:
                category_quality_clean = category_quality_clean.drop('All', axis=1)

            fig = px.imshow(
                category_quality_clean.values,
                x=category_quality_clean.columns,
                y=category_quality_clean.index,
                title='Categories vs Answer Quality Heatmap',
                color_continuous_scale='Viridis'
            )
            st.plotly_chart(fig, use_container_width=True)

    with tab2:
        # Subcategory analysis with filtering
        st.subheader("Subcategory (Topic) Analysis")

        # Filter by category
        selected_category = st.selectbox(
            "Filter by Category:",
            options=['All'] + list(category_analysis['topic_group_distribution'].index)
        )

        if selected_category == 'All':
            filtered_topics = category_analysis['topic_distribution']
        else:
            filtered_df = df[df['topic_group'] == selected_category]
            filtered_topics = filtered_df['topic'].value_counts()

        # Display top subcategories
        col1, col2 = st.columns(2)

        with col1:
            # Top subcategories table
            st.write(f"**Top 20 Subcategories{' in ' + selected_category if selected_category != 'All' else ''}:**")
            top_topics_df = pd.DataFrame({
                'Subcategory': filtered_topics.head(20).index,
                'Count': filtered_topics.head(20).values
            })
            st.dataframe(top_topics_df, use_container_width=True)

        with col2:
            # Subcategory pie chart (top 10)
            top_10_topics = filtered_topics.head(10)
            fig = px.pie(
                values=top_10_topics.values,
                names=top_10_topics.index,
                title=f'Top 10 Subcategories{" in " + selected_category if selected_category != "All" else ""}'
            )
            st.plotly_chart(fig, use_container_width=True)

    with tab3:
        # Interactive filtering by category
        st.subheader("Filter Questions by Category")

        col1, col2 = st.columns(2)

        with col1:
            filter_category = st.selectbox(
                "Select Category:",
                options=['All'] + list(category_analysis['topic_group_distribution'].index),
                key="category_filter"
            )

        with col2:
            # Get subcategories for selected category
            if filter_category == 'All':
                available_subcategories = ['All'] + list(category_analysis['topic_distribution'].index)
            else:
                subcategories = df[df['topic_group'] == filter_category]['topic'].value_counts()
                available_subcategories = ['All'] + list(subcategories.index)

            filter_subcategory = st.selectbox(
                "Select Subcategory:",
                options=available_subcategories,
                key="subcategory_filter"
            )

        # Apply filters
        filtered_category_df = df.copy()

        if filter_category != 'All':
            filtered_category_df = filtered_category_df[filtered_category_df['topic_group'] == filter_category]

        if filter_subcategory != 'All':
            filtered_category_df = filtered_category_df[filtered_category_df['topic'] == filter_subcategory]

        # Display filtered results
        st.write(f"**Filtered Results: {len(filtered_category_df)} questions**")

        if len(filtered_category_df) > 0:
            # Quality distribution for filtered data
            quality_dist = filtered_category_df['answer_quality'].value_counts()

            col1, col2 = st.columns(2)

            with col1:
                fig = px.bar(
                    x=quality_dist.index,
                    y=quality_dist.values,
                    title='Answer Quality Distribution (Filtered)'
                )
                st.plotly_chart(fig, use_container_width=True)

            with col2:
                # Sample questions from filtered data
                sample_size = min(5, len(filtered_category_df))
                sample_questions = filtered_category_df.sample(n=sample_size)

                st.write(f"**Sample Questions ({sample_size}):**")
                for idx, row in sample_questions.iterrows():
                    with st.expander(f"Q: {row['question'][:100]}..."):
                        st.write("**Full Question:**")
                        st.write(row['question'])
                        st.write("**Full Answer:**")
                        st.write(row['answer'])
                        st.write(f"**Category:** {row['topic_group']}")
                        st.write(f"**Subcategory:** {row['topic']}")
                        st.write(f"**Quality:** {row['answer_quality']}")
                        st.write(f"**User ID:** {row['user_id']}")
                        st.write(f"**Pharmacy ID:** {row['pharmacy_id']}")


def render_sample_explorer(df: pd.DataFrame, pharmacy_analysis: Dict[str, Any], user_analysis: Dict[str, Any]):
    st.header("🔍 Enhanced Sample Data Explorer")

    # First row of filters
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # Pharmacy selector
        selected_pharmacy = st.selectbox(
            "Select a Pharmacy:",
            options=['All'] + list(pharmacy_analysis['pharmacy_stats']['pharmacy_id'].head(20))
        )

    with col2:
        # User selector
        selected_user = st.selectbox(
            "Select a User:",
            options=['All'] + list(user_analysis['user_stats']['user_id'].head(20))
        )

    with col3:
        # Answer quality selector
        selected_quality = st.selectbox(
            "Select Answer Quality:",
            options=['All'] + list(df['answer_quality'].unique())
        )

    with col4:
        # Tags selector
        selected_tag = st.selectbox(
            "Select Tag:",
            options=['All'] + list(df['tags_clean'].unique())
        )

    # Additional filter row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # No answer generated filter
        filter_no_answer_generated = st.selectbox(
            "No Answer Generated:",
            options=['All', 'Exclude', 'Only'],
            help="Filter by 'i-do-not-even-know-what-to-answer-to-this' responses"
        )

    with col2:
        # Category filter
        selected_category = st.selectbox(
            "Select Category:",
            options=['All'] + list(df['topic_group'].unique()),
            key="sample_category_filter"
        )

    with col3:
        # Subcategory filter (depends on category selection)
        if selected_category == 'All':
            available_subcategories = ['All'] + list(df['topic'].unique())
        else:
            subcategories = df[df['topic_group'] == selected_category]['topic'].unique()
            available_subcategories = ['All'] + list(subcategories)

        selected_subcategory = st.selectbox(
            "Select Subcategory:",
            options=available_subcategories,
            key="sample_subcategory_filter"
        )

    # Second row for sorting and display options
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # Sort by selector
        sort_by = st.selectbox(
            "Sort by:",
            options=['Date (Newest First)', 'Date (Oldest First)', 'Pharmacy ID', 'User ID', 'Answer Quality', 'Random']
        )

    with col2:
        # Number of samples selector
        sample_size_option = st.selectbox(
            "Number of samples:",
            options=[5, 10, 20, 50, 100],
            index=1  # Default to 10
        )

    with col3:
        # Show metadata toggle
        show_metadata = st.checkbox("Show detailed metadata", value=True)

    with col4:
        # Show full answer toggle
        show_full_answer = st.checkbox("Show full answers", value=True)

    # Filter data based on selection
    filtered_df = df.copy()

    if selected_pharmacy != 'All':
        filtered_df = filtered_df[filtered_df['pharmacy_id'] == selected_pharmacy]

    if selected_user != 'All':
        filtered_df = filtered_df[filtered_df['user_id'] == selected_user]

    if selected_quality != 'All':
        filtered_df = filtered_df[filtered_df['answer_quality'] == selected_quality]

    if selected_tag != 'All':
        filtered_df = filtered_df[filtered_df['tags_clean'] == selected_tag]

    # Apply no_answer_generated filter
    if filter_no_answer_generated == 'Exclude':
        filtered_df = filtered_df[~filtered_df['no_answer_generated']]
    elif filter_no_answer_generated == 'Only':
        filtered_df = filtered_df[filtered_df['no_answer_generated']]

    # Apply category filters
    if selected_category != 'All':
        filtered_df = filtered_df[filtered_df['topic_group'] == selected_category]

    if selected_subcategory != 'All':
        filtered_df = filtered_df[filtered_df['topic'] == selected_subcategory]

    if len(filtered_df) > 0:
        st.subheader(f"Sample Questions ({len(filtered_df)} total)")

        # Show active filters summary
        active_filters = []
        if selected_pharmacy != 'All':
            active_filters.append(f"Pharmacy: {selected_pharmacy}")
        if selected_user != 'All':
            active_filters.append(f"User: {selected_user}")
        if selected_quality != 'All':
            active_filters.append(f"Quality: {selected_quality}")
        if selected_tag != 'All':
            active_filters.append(f"Tag: {selected_tag}")
        if filter_no_answer_generated != 'All':
            active_filters.append(f"No Answer Generated: {filter_no_answer_generated}")
        if selected_category != 'All':
            active_filters.append(f"Category: {selected_category}")
        if selected_subcategory != 'All':
            active_filters.append(f"Subcategory: {selected_subcategory}")

        if active_filters:
            st.info(f"**Active filters:** {' | '.join(active_filters)}")

        # Apply sorting
        if sort_by == 'Date (Newest First)':
            if 'created_at' in filtered_df.columns:
                filtered_df = filtered_df.sort_values('created_at', ascending=False)
            else:
                st.warning("No date column available for sorting")
        elif sort_by == 'Date (Oldest First)':
            if 'created_at' in filtered_df.columns:
                filtered_df = filtered_df.sort_values('created_at', ascending=True)
            else:
                st.warning("No date column available for sorting")
        elif sort_by == 'Pharmacy ID':
            filtered_df = filtered_df.sort_values('pharmacy_id')
        elif sort_by == 'User ID':
            filtered_df = filtered_df.sort_values('user_id')
        elif sort_by == 'Answer Quality':
            # Define custom order for answer quality
            quality_order = ['Correct', 'Partial', 'Incorrect', 'No Answer', 'Excluded', 'Unknown']
            filtered_df['quality_order'] = filtered_df['answer_quality'].apply(
                lambda x: quality_order.index(x) if x in quality_order else len(quality_order)
            )
            filtered_df = filtered_df.sort_values('quality_order')
            filtered_df = filtered_df.drop('quality_order', axis=1)

        # Select sample based on sorting and sample size
        try:
            if sort_by == 'Random':
                sample_size = min(sample_size_option, len(filtered_df))
                sample_df = filtered_df.sample(n=sample_size)[
                    ['question', 'answer', 'user_id', 'pharmacy_id', 'answer_quality',
                     'tags_clean', 'grade_selector', 'no_answer_generated', 'topic_group', 'topic', 'created_at']
                ]
            else:
                sample_size = min(sample_size_option, len(filtered_df))
                sample_df = filtered_df.head(sample_size)[
                    ['question', 'answer', 'user_id', 'pharmacy_id', 'answer_quality',
                     'tags_clean', 'grade_selector', 'no_answer_generated', 'topic_group', 'topic', 'created_at']
                ]
        except Exception as e:
            st.error(f"Error selecting sample data: {e}")
            st.write(f"Available columns: {list(filtered_df.columns)}")
            return

        # Display sorting information
        if sort_by != 'Random':
            st.info(f"Showing {len(sample_df)} samples sorted by: {sort_by}")
        else:
            st.info(f"Showing {len(sample_df)} random samples")

        # Check if we have data to display
        if len(sample_df) == 0:
            st.warning("No samples found matching the current filters.")
            return

        for idx, row in sample_df.iterrows():
            try:
                # Create a more informative title
                quality_emoji = {
                    'Correct': '✅',
                    'Incorrect': '❌',
                    'Partial': '⚠️',
                    'No Answer': '🚫',
                    'Excluded': '🔒',
                    'Unknown': '❔'
                }.get(row['answer_quality'], '❔')

                tag_emoji = {
                    'WAP': '🏷️',
                    'Not WAP': '📝',
                    'No Tag': '⚪'
                }.get(row['tags_clean'], '⚪')

                # Add date to title if available and sorting by date
                date_info = ""
                if 'created_at' in row and pd.notna(row['created_at']) and sort_by.startswith('Date'):
                    try:
                        date_str = pd.to_datetime(row['created_at']).strftime('%Y-%m-%d %H:%M')
                        date_info = f" ({date_str})"
                    except:
                        pass

                # Add no answer generated indicator
                no_answer_indicator = " 🚫" if row.get('no_answer_generated', False) else ""

                title = f"{quality_emoji} {tag_emoji} User {row['user_id']} at Pharmacy {row['pharmacy_id']}{date_info}{no_answer_indicator}"

                with st.expander(title):
                    if show_metadata:
                        col1, col2 = st.columns([2, 1])
                    else:
                        col1, col2 = st.columns([1, 0])

                    with col1:
                        st.write("**Question:**")
                        st.write(row['question'])

                        if show_full_answer:
                            st.write("**Answer:**")
                            st.write(row['answer'])
                        else:
                            # Show truncated answer
                            answer_preview = str(row['answer'])[:200] + "..." if len(str(row['answer'])) > 200 else str(
                                row['answer'])
                            st.write("**Answer (preview):**")
                            st.write(answer_preview)
                            if len(str(row['answer'])) > 200:
                                if st.button(f"Show full answer", key=f"full_answer_{idx}"):
                                    st.write("**Full Answer:**")
                                    st.write(row['answer'])

                    if show_metadata:
                        with col2:
                            st.write("**Metadata:**")
                            st.write(f"**Quality:** {row['answer_quality']}")
                            st.write(f"**Tag:** {row['tags_clean']}")
                            st.write(f"**Grade Selector:** {row['grade_selector']}")
                            st.write(
                                f"**No Answer Generated:** {'Yes' if row.get('no_answer_generated', False) else 'No'}")
                            st.write(f"**Category:** {row['topic_group']}")
                            st.write(f"**Subcategory:** {row['topic']}")
                            if 'created_at' in row and pd.notna(row['created_at']):
                                st.write(f"**Date:** {row['created_at']}")
                            st.write(f"**User ID:** {row['user_id']}")
                            st.write(f"**Pharmacy ID:** {row['pharmacy_id']}")

            except Exception as e:
                st.error(f"Error displaying sample {idx}: {e}")
                st.write(f"Row data: {dict(row)}")

    else:
        st.info("No data found for the selected filters.")


def create_streamlit_dashboard(file_path: str):
    """
    Create interactive Streamlit dashboard.

    Args:
        file_path: Path to the CSV file
    """
    # Load all data (cached) - this will only run once and be cached for subsequent widget changes
    df, pharmacy_analysis, user_analysis, summary_stats, answer_quality_analysis, tags_analysis, category_analysis = get_all_analysis_data(file_path)

    st.title("🏥 WAP Chat Analysis Dashboard")
    st.markdown("---")

    render_summary_statistics(summary_stats, answer_quality_analysis, tags_analysis)
    st.markdown("---")

    render_pharmacy_analysis(pharmacy_analysis)
    st.markdown("---")

    render_user_analysis(user_analysis)
    st.markdown("---")

    render_answer_quality_analysis(answer_quality_analysis)
    st.markdown("---")

    render_tags_analysis(df, tags_analysis)
    st.markdown("---")

    render_category_analysis(df, category_analysis)
    st.markdown("---")

    render_sample_explorer(df, pharmacy_analysis, user_analysis)


def export_results_to_csv(
    pharmacy_analysis: Dict,
    user_analysis: Dict,
    summary_stats: Dict,
    answer_quality_analysis: Dict,
    tags_analysis: Dict,
    category_analysis: Dict,
    output_dir: str
):
    """
    Export analysis results to CSV files.

    Args:
        pharmacy_analysis: Pharmacy analysis results
        user_analysis: User analysis results
        summary_stats: Summary statistics
        answer_quality_analysis: Answer quality analysis results
        tags_analysis: Tags analysis results
        category_analysis: Category analysis results
        output_dir: Output directory
    """
    import os
    os.makedirs(output_dir, exist_ok=True)

    # Export pharmacy stats
    pharmacy_analysis['pharmacy_stats'].to_csv(f'{output_dir}/pharmacy_stats.csv', index=False)

    # Export user stats
    user_analysis['user_stats'].to_csv(f'{output_dir}/user_stats.csv', index=False)

    # Export multi-pharmacy users
    if len(user_analysis['multi_pharmacy_users']) > 0:
        user_analysis['multi_pharmacy_users'].to_csv(f'{output_dir}/multi_pharmacy_users.csv', index=False)

    # Export answer quality analysis
    answer_quality_analysis['quality_distribution'].to_csv(f'{output_dir}/answer_quality_distribution.csv', index=False)
    answer_quality_analysis['pharmacy_quality'].to_csv(f'{output_dir}/pharmacy_answer_quality.csv', index=False)
    answer_quality_analysis['tag_quality'].to_csv(f'{output_dir}/tag_answer_quality.csv', index=False)

    # Export tags analysis
    tags_analysis['tag_distribution'].to_csv(f'{output_dir}/tag_distribution.csv', index=False)
    tags_analysis['pharmacy_tag_stats'].to_csv(f'{output_dir}/pharmacy_tag_stats.csv', index=False)
    tags_analysis['user_tag_stats'].to_csv(f'{output_dir}/user_tag_stats.csv', index=False)

    # Export category analysis
    category_analysis['topic_group_distribution'].to_csv(f'{output_dir}/category_distribution.csv')
    category_analysis['topic_distribution'].to_csv(f'{output_dir}/subcategory_distribution.csv')
    category_analysis['pharmacy_category_stats'].to_csv(f'{output_dir}/pharmacy_category_stats.csv', index=False)
    category_analysis['user_category_stats'].to_csv(f'{output_dir}/user_category_stats.csv', index=False)
    category_analysis['category_quality_cross'].to_csv(f'{output_dir}/category_quality_cross.csv')
    category_analysis['topic_quality_cross'].to_csv(f'{output_dir}/topic_quality_cross.csv')

    # Export summary stats
    summary_df = pd.DataFrame([summary_stats])
    summary_df.to_csv(f'{output_dir}/summary_stats.csv', index=False)

    print(f"Results exported to {output_dir}/")


def main():
    """
    Main function to run the WAP chat analysis.
    """
    args = parse_args()

    # Load and process data
    df = load_and_process_data(args.file)

    # Run visualization based on mode
    if args.mode == 'streamlit':
        # print(f"\nLaunching Streamlit dashboard...")
        # print("Note: The dashboard will open in your default web browser.")
        # print("If running this script directly, the dashboard will be created but not launched.")
        # print("To launch manually, run: streamlit run scripts/wap_analysis.py -- -f <your_file>")

        # Create Streamlit dashboard - all analysis will be cached
        create_streamlit_dashboard(args.file)

    elif args.mode == 'static':
        if len(df) == 0:
            print("No valid data found after processing. Please check your CSV file.")
            return

        # Generate analyses
        print("Generating summary statistics...")
        summary_stats = generate_summary_stats(df)

        print("Analyzing pharmacy data...")
        pharmacy_analysis = create_pharmacy_analysis(df)

        print("Analyzing user data...")
        user_analysis = create_user_analysis(df)

        print("Analyzing answer quality...")
        answer_quality_analysis = create_answer_quality_analysis(df)

        print("Analyzing tags...")
        tags_analysis = create_tags_analysis(df)

        print("Analyzing categories...")
        category_analysis = create_category_analysis(df)

        # Print summary to console
        print("\n" + "=" * 50)
        print("WAP CHAT ANALYSIS SUMMARY")
        print("=" * 50)
        print(f"Total Questions: {summary_stats['total_questions']:,}")
        print(f"Unique Pharmacies: {summary_stats['unique_pharmacies']:,}")
        print(f"Unique Users: {summary_stats['unique_users']:,}")
        print(f"Avg Questions per Pharmacy: {summary_stats['avg_questions_per_pharmacy']:.1f}")
        print(f"Avg Questions per User: {summary_stats['avg_questions_per_user']:.1f}")

        print(f"\nTop 5 Pharmacies by Question Count:")
        top_5_pharmacies = pharmacy_analysis['top_10_pharmacies'].head(5)
        for idx, row in top_5_pharmacies.iterrows():
            print(f"  Pharmacy {row['pharmacy_id']}: {row['question_count']} questions, {row['user_count']} users")

        print(f"\nTop 5 Users by Question Count:")
        top_5_users = user_analysis['top_10_users'].head(5)
        for idx, row in top_5_users.iterrows():
            print(f"  User {row['user_id']}: {row['question_count']} questions, {row['pharmacy_count']} pharmacies")

        print(f"\nAnswer Quality Summary:")
        quality_stats = answer_quality_analysis['quality_stats']
        print(f"  Overall Correct Rate: {quality_stats['overall_correct_rate']:.1f}%")
        print(f"  Correct Answers: {quality_stats['correct_answers']:,}")
        print(f"  Incorrect Answers: {quality_stats['incorrect_answers']:,}")
        print(f"  Partial Answers: {quality_stats['partial_answers']:,}")
        print(f"  No Answers: {quality_stats['no_answers']:,}")
        print(f"  Unknown Answers: {quality_stats['unknown_answers']:,}")
        print(
            f"  No Answer Generated: {quality_stats['no_answer_generated']:,} ({quality_stats['no_answer_generated_rate']:.1f}%)")

        print(f"\nTags Summary:")
        tag_stats = tags_analysis['tag_stats']
        print(f"  WAP Questions: {tag_stats['wap_questions']:,} ({tag_stats['wap_percentage']:.1f}%)")
        print(f"  Not WAP Questions: {tag_stats['not_wap_questions']:,}")
        print(f"  No Tag Questions: {tag_stats['no_tag_questions']:,}")

        print(f"\nCategory Summary:")
        category_stats = category_analysis['category_stats']
        print(f"  Total Categories: {category_stats['unique_topic_groups']:,}")
        print(f"  Total Subcategories: {category_stats['unique_topics']:,}")
        print(f"  Total Questions: {category_stats['total_questions']:,}")

        print(f"\nTop 5 Categories:")
        top_categories = category_analysis['topic_group_distribution'].head(5)
        for category, count in top_categories.items():
            percentage = (count / category_stats['total_questions']) * 100
            print(f"  {category}: {count:,} ({percentage:.1f}%)")

        # Export CSV if requested
        if args.export_csv:
            print(f"\nExporting results to CSV files...")
            export_results_to_csv(pharmacy_analysis, user_analysis, summary_stats,
                                  answer_quality_analysis, tags_analysis, category_analysis, args.output)

        print(f"\nGenerating static visualizations...")
        create_static_visualizations(df, pharmacy_analysis, user_analysis, args.output)
        print(f"Static charts saved to {args.output}/")

        print("\nAnalysis completed!")


def parse_args():
    parser = argparse.ArgumentParser(description='Analyze WAP chat data by pharmacy and user')
    parser.add_argument('-f', '--file', required=True, help='Path to the CSV file')
    parser.add_argument('-o', '--output', default='wap_analysis_results', help='Output directory for results')
    parser.add_argument(
        '--mode',
        choices=['streamlit', 'static'],
        default='streamlit',
        help='Analysis mode: streamlit for interactive dashboard, static for matplotlib charts'
    )
    parser.add_argument('--export-csv', action='store_true', help='Export analysis results to CSV files')
    return parser.parse_args()


if __name__ == '__main__':
    main()
