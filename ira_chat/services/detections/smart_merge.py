import asyncio
import copy
import logging
import json
import re

from langchain_core.language_models import BaseChatModel


logger = logging.getLogger(__name__)

EMPTY_VALUES = {"", None, 0}
NA_VALUES = {"N/A", "Not provided", "", None, 0}
MERGE_LISTS_PROMPT = """Merge two JSON lists intelligently to produce a consolidated JSON list. Follow these rules:

1. **Object Matching Strategy**: Determine if objects are the same using these criteria in order of priority:
   - **Unique IDs**: Match on id, external_id, uid, or other unique identifiers
   - **Business Keys**: Match on domain-specific unique combinations:
     * Person: firstname+lastname, full_name, or email (if substantial)
     * Address: street+city+state, or street+zip (normalize street variations like "St"/"Street")
     * Bank Account: account_number+routing_number, or account_number+bank_name
     * Registration: registration_number+state, or entity_name+state+legal_entity_type
     * Contact: type+value (email/phone), or email/phone if unique
     * Service/Subscription: title+price_type, or service_id
     * Task: title+client_id+due_date, or external task identifiers
     * Card: last_4_digits+card_holder_name, or card_number (if available)
   - **Fuzzy Matching**: If no exact match, consider objects with 70%+ meaningful field overlap
   - **Type Consistency**: Only match objects of the same logical type

2. **Field Value Selection**: When merging matched objects, choose the best value for each field:
   - **Prefer non-empty**: Choose non-empty over ("", null, 0, "N/A", "Not provided", "None")
   - **Prefer detailed**: Choose longer, more specific, or formatted values over generic ones
   - **Prefer from "two"**: When both values are equally valid, prefer the value from list "two"
   - **Preserve all fields**: Include all unique fields from both objects (union of fields)
   - **Preserve all ids**: Keep all "id", "***_id", "uid", or other unique identifiers
   - **Handle conflicts**: For conflicting non-empty values, prefer the more complete one from "two"

3. **Unmatched Objects**: Include all objects that have no match in the other list

4. **Output**: Return only the merged JSON list, no explanations

List one:
{one}

List two:
{two}

Merged JSON list:
"""


async def amerge_jsons(jsons: list[dict], llm: BaseChatModel = None) -> dict:
    if not jsons:
        return {}
    if len(jsons) == 1:
        return jsons[0]

    result = jsons[0]
    mode = 'overwrite_append' if not llm else 'overwrite_llm'
    for obj in jsons[1:]:
        result = await adict_merge(result, obj, mode=mode, llm=llm, verbose=True)

    return result


async def _merge_list_with_llm(
    llm: BaseChatModel,
    left_list: list,
    right_list: list,
    verbose: bool,
    prompt: str = MERGE_LISTS_PROMPT
) -> list:
    # Optimize for empty lists
    if not left_list:
        return right_list
    if not right_list:
        return left_list
    prompt = prompt.format(
        one=json.dumps(left_list, indent=2),
        two=json.dumps(right_list, indent=2),
    )
    if verbose:
        logger.info(f"[MERGE] Prompt: {prompt}")
    llm_output = await llm.ainvoke(prompt)
    if verbose:
        logger.info(f"[MERGE] Output: {llm_output.content}")
    return extract_json_list(llm_output.content)


def extract_json_list(text):
    match = re.search(r"(\[.*?\])", text, re.DOTALL)
    if match:
        json_str = match.group(1).strip()
        # TODO: seems like it breaks special symbols including ó or á in translations
        # json_str = json_str.replace("\\", "\\\\")
        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            return json.loads(text[text.index('['):text.rindex(']')+1])
    else:
        logger.warning("No JSON list block found in the input text")
        return None


async def adict_merge(
    dct_left: dict,
    merge_dct: dict,
    mode: str = 'overwrite',
    check_keys: bool = False,
    copy_data: bool = True,
    llm: BaseChatModel = None,
    verbose: bool = False,
    prompt: str = MERGE_LISTS_PROMPT,
) -> dict:
    """
    Recursive dict merge. The method recurse down into dicts nested
    to an arbitrary depth, updating keys. The ``merge_dct`` is merged into ``dct_left``.

    Parameters
    ----------
    dct_left: dict
        Dictionary onto which the merge is executed

    merge_dct:dict
        Dictionary merged into dct

    mode:str, optional, default='safe'
        3 modes are allowed: "safe" or "overwrite" or "overwrite_append" or 'overwrite_llm'.
        "safe" will raise error if the 2 dicts have the same key and different values
        while "overwrite" will overwrite ``dct`` with the value of ``merge_dct``

    check_keys: bool
        Should the method check if keys from ``merge_dct`` are present in ``dct_left``
        and throw an error in case they are not

    copy_data: copy the source dict before editing or not. Default is True.
    llm: Use llm for smart list merging.
    verbose: log LLM usage or not
    prompt: prompt for LLM

    Returns
    -------
    dict
        Merged dict
    """

    if not merge_dct:
        return dct_left

    dct = copy.deepcopy(dct_left) if copy_data else dct_left

    if mode not in ("safe", "overwrite", "overwrite_append", "overwrite_llm"):
        raise ValueError(f"dict_merge mode '{mode}' is not supported")

    # collect llm list-merge tasks
    llm_tasks = []
    # to remember where to store results: (key, parent_dict, task_index)
    llm_targets = []

    for k, v in merge_dct.items():

        if k not in dct.keys() and check_keys:
            raise Exception(f"Cannot overlay non existing config item '{k}'")

        if k in dct and isinstance(dct[k], dict) and isinstance(merge_dct[k], dict):
            dct[k] = await adict_merge(dct[k], merge_dct[k], mode, copy_data=False, check_keys=check_keys, llm=llm, verbose=verbose)
        elif k not in dct and isinstance(merge_dct[k], dict):
            dct[k] = merge_dct[k]
        elif mode == 'overwrite_llm' and k in dct and isinstance(dct[k], list) and isinstance(merge_dct[k], list):
            if llm is None:
                raise RuntimeError("LLM required for overwrite_llm mode")
            task = asyncio.create_task(_merge_list_with_llm(llm, dct.get(k), merge_dct[k], verbose, prompt))
            llm_targets.append((k, dct, len(llm_tasks)))
            llm_tasks.append(task)
        elif mode in ("overwrite", "overwrite_append", "overwrite_llm") and k in dct and isinstance(dct[k], list) and isinstance(merge_dct[k], list):
            if len(dct[k]) > 0:
                exceptions = list(NA_VALUES)
                need_replace = isinstance(dct[k][0], dict) and all(dct[k][0][kk] in exceptions for kk in dct[k][0])
                need_append = True
                if len(merge_dct[k]) > 0 and isinstance(merge_dct[k][0], dict) and isinstance(dct[k][0], dict):
                    # Use the keys from merge_dct[k][0] to check values, not dct[k][0]
                    need_append = not all(merge_dct[k][0].get(kk) in exceptions for kk in merge_dct[k][0])

                if dct[k][0] is None or need_replace:
                    dct[k] = merge_dct[k]
                else:
                    if need_append or not isinstance(merge_dct[k][0], dict):
                        dct[k] = dct[k] + merge_dct[k]
            else:
                dct[k] = dct[k] + merge_dct[k]
        elif mode in ("overwrite", "overwrite_append", "overwrite_llm") and k in dct and isinstance(dct[k], list) and not isinstance(merge_dct[k], list):
            # Disallow overwrite list by a scalar
            pass
        else:
            if mode == "safe":

                if k in dct and dct[k] is not None and dct[k] != merge_dct[k]:
                    raise Exception(
                        f"Trying to overwrite parameter '{k}' of value '{dct[k]}' "
                        f"with value '{merge_dct[k]}'. Operation not allowed in 'safe' mode"
                    )
                else:
                    dct[k] = merge_dct[k]

            if mode in ("overwrite", "overwrite_append", "overwrite_llm"):
                is_list_right = isinstance(merge_dct.get(k), list)
                is_list_left = isinstance(dct.get(k), list)

                if is_list_left and is_list_right:
                    dct[k] = merge_dct[k]
                    continue

                # Check if value is a dict before doing membership test with NA_VALUES
                is_merge_val_dict = isinstance(merge_dct.get(k), dict)
                is_dct_val_dict = isinstance(dct.get(k), dict)

                # For dictionaries, we consider them "not in NA_VALUES" by default
                merge_val_not_in_na = is_merge_val_dict or (not is_list_right and merge_dct.get(k) not in NA_VALUES)
                dct_val_in_empty = is_dct_val_dict or (not is_list_left and dct.get(k) in EMPTY_VALUES)

                if merge_val_not_in_na or k not in dct or dct_val_in_empty:
                    if merge_dct[k] is not None or k not in dct:
                        dct[k] = merge_dct[k]

    # await all LLM list-merge tasks and assign back
    if llm_tasks:
        results = await asyncio.gather(*llm_tasks)
        for key, parent, idx in llm_targets:
            parent[key] = results[idx]

    return dct
