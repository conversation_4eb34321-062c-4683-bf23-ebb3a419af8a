
import datetime
from typing import Optional, List

from pydantic import BaseModel, field_validator
from records.utils import json_utils


class AddressBase(BaseModel):
    city: Optional[str] = None
    full_address: Optional[str] = None
    pobox: Optional[str] = None
    state: Optional[str] = None
    zip: Optional[str] = None


class AddressCreate(AddressBase):
    street: str
    country: str


class AddressUpdate(AddressBase):
    street: Optional[str] = None
    country: Optional[str] = None


class PersonBase(BaseModel):
    id: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    contact_info: Optional[str] = None
    citizenship: Optional[str] = None
    address: Optional[str] = None
    companies: Optional[str] = None


class PersonCreate(PersonBase):
    firstname: str
    lastname: str


class PersonUpdate(PersonBase):
    firstname: Optional[str] = None
    lastname: Optional[str] = None


class RegAgentBase(BaseModel):
    nickname: Optional[str] = None
    address_id: Optional[str] = None
    address: Optional[AddressUpdate] = None


class RegAgentCreate(RegAgentBase):
    id: Optional[str] = None
    title: str


class RegAgentUpdate(RegAgentBase):
    id: Optional[str] = None
    title: Optional[str] = None


class BankAccountCreate(BaseModel):
    bank_name: str
    aba_number: str
    account_number: Optional[str] = None
    controlled_by: Optional[str] = None
    authorized_signer_id: Optional[str] = None
    bank_contact: str
    date_opened: datetime.datetime
    last_renewal: datetime.datetime
    notes: str

    _date_validator = field_validator(
        *['date_opened', 'last_renewal'],
        mode='before'
    )(json_utils.date_validator)


class BankCardCreate(BaseModel):
    card_number: Optional[str] = None
    id: Optional[int] = None
    last_4_digits: Optional[str] = None
    cvv: Optional[str] = None
    card_holder_name: Optional[str] = None
    valid_through: Optional[str] = None
    expired_at: Optional[datetime.datetime] = None

    _date_validator = field_validator(
        *['expired_at'],
        mode='before'
    )(json_utils.date_validator)


class BankAccountAuthorizedSigner(BaseModel):
    id: Optional[int] = None
    client_person_id: Optional[str] = None
    person: Optional[PersonUpdate] = None


class BankAccountUpdate(BaseModel):
    id: Optional[str] = None
    bank_name: Optional[str] = None
    aba_number: Optional[str] = None
    account_number: Optional[str] = None
    bank_contact: Optional[str] = None
    controlled_by: Optional[str] = None
    date_opened: Optional[datetime.datetime] = None
    last_renewal: Optional[datetime.datetime] = None
    notes: Optional[str] = None

    authorized_signers: Optional[List[BankAccountAuthorizedSigner]] = None
    bank_cards: Optional[List[BankCardCreate]] = None

    _date_validator = field_validator(
        *['date_opened', 'last_renewal'],
        mode='before'
    )(json_utils.date_validator)


class ClientBase(BaseModel):
    accounting_method: Optional[str] = None
    active_since: Optional[datetime.datetime] = None
    agr_signed: Optional[datetime.datetime] = None
    agreement_sum: Optional[float] = None
    billing_method: Optional[str] = None
    bookkeeping: Optional[bool] = None
    business_model: Optional[str] = None
    company_phone: Optional[str] = None
    cpa: Optional[str] = None
    description: Optional[str] = None
    dissolution_date: Optional[datetime.datetime] = None
    ein: Optional[str] = None
    fedtaxforms: Optional[str] = None
    financial_year_end: Optional[str] = None
    financial_year_end_for_subsidiary: Optional[str] = None
    incorp_by: Optional[str] = None
    legal_ent_type: Optional[str] = None
    monthly_bill: Optional[float] = None
    naicscode: Optional[str] = None
    notes_accounting: Optional[str] = None
    notes_address: Optional[str] = None
    notes_agreement: Optional[str] = None
    notes_contacts: Optional[str] = None
    notes_main: Optional[str] = None
    notes_shareholders: Optional[str] = None
    optional_share_count: Optional[int] = None
    paid_by: Optional[str] = None
    paid_by_mail: Optional[str] = None
    payroll: Optional[bool] = None
    renewal_date: Optional[datetime.datetime] = None
    renewal_date_mail: Optional[datetime.datetime] = None
    since: Optional[datetime.datetime] = None
    statetaxforms: Optional[str] = None
    status: Optional[str] = None
    subjurisd: Optional[str] = None
    subsidiary_legal_entity_type: Optional[str] = None
    subsidiary_to_consolidate: Optional[str] = None
    total_shares: Optional[int] = None
    withdrawal_date: Optional[datetime.datetime] = None

    # connections ids
    source_id: Optional[str] = None
    manager_id: Optional[str] = None

    _date_validator = field_validator(
        *['active_since', 'agr_signed', 'dissolution_date', 'renewal_date',
          'renewal_date_mail', 'since', 'withdrawal_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientManager(BaseModel):
    id: Optional[str] = None
    user_id: Optional[int] = None
    title: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    role_name: Optional[str] = None


class ClientAddress(BaseModel):
    id: Optional[str] = None
    address_type: Optional[str] = None
    renewal_date: Optional[datetime.datetime] = None
    phone: Optional[str] = None
    paid_by: Optional[str] = None
    note: Optional[str] = None
    address_id: Optional[str] = None
    address: Optional[AddressCreate] = None

    _date_validator = field_validator(
        *['renewal_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientAuthorizedSigner(BaseModel):
    id: Optional[int] = None
    client_person_id: Optional[str] = None
    note: Optional[str] = None
    person: Optional[PersonCreate] = None


class Service(BaseModel):
    id: Optional[str] = None
    title: Optional[str] = None
    price: Optional[float] = None
    price_type: Optional[str] = None


class ClientService(BaseModel):
    id: Optional[int] = None
    service_id: Optional[str] = None
    service: Optional[Service] = None
    active_since: Optional[datetime.datetime] = None
    active_until: Optional[datetime.datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None

    _date_validator = field_validator(
        *['active_since', 'active_until'],
        mode='before'
    )(json_utils.date_validator)


class ClientTask(BaseModel):
    id: Optional[int] = None
    date: Optional[datetime.datetime] = None
    task: Optional[str] = None
    due_date: Optional[datetime.datetime] = None
    status: Optional[str] = None
    manager_id: Optional[str] = None
    manager: Optional[ClientManager] = None

    _date_validator = field_validator(
        *['date', 'due_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientTaxReporting(BaseModel):
    id: Optional[int] = None
    year: Optional[str] = None
    reporting_1099: Optional[str] = None
    tax_return_by: Optional[str] = None
    note: Optional[str] = None
    files: Optional[str] = None


class ClientLLCShareholder(BaseModel):
    id: Optional[str] = None
    position: Optional[str] = None
    is_managing_member: Optional[bool] = None
    ownership: Optional[str] = None
    note: Optional[str] = None
    client_person_id: Optional[str] = None
    person: Optional[PersonCreate] = None


class ClientRegistration(BaseModel):
    id: Optional[int] = None
    registered_agent_id: Optional[str] = None
    state_of_incorporation: Optional[str] = None
    registered_date: Optional[datetime.datetime] = None
    terminated_date: Optional[datetime.datetime] = None
    last_renewal_date: Optional[datetime.datetime] = None
    annual_compliance_due_date: Optional[datetime.datetime] = None
    billed_to: Optional[str] = None
    last_soi_filed: Optional[datetime.datetime] = None
    state_entity: Optional[str] = None
    notes: Optional[str] = None
    registered_agent: Optional[RegAgentCreate] = None

    _date_validator = field_validator(
        *['registered_date', 'terminated_date', 'last_renewal_date', 'annual_compliance_due_date', 'last_soi_filed'],
        mode='before'
    )(json_utils.date_validator)


class ClientShare(BaseModel):
    id: Optional[int] = None
    type: Optional[str] = None
    stock_authorized: Optional[int] = None
    stock_issued: Optional[int] = None
    notes: Optional[str] = None

    date: Optional[datetime.datetime] = None

    _date_validator = field_validator(
        *['date'],
        mode='before'
    )(json_utils.date_validator)


class ClientCapitalization(BaseModel):
    id: Optional[int] = None
    person_id: Optional[str] = None
    share_id: Optional[int] = None
    share_type: Optional[str] = None
    share_amount: Optional[int] = None
    date: Optional[datetime.datetime] = None
    issued_percentage: Optional[float] = None
    authorized_percentage: Optional[float] = None
    notes: Optional[str] = None
    person: Optional[PersonCreate] = None
    share: Optional[ClientShare] = None

    _date_validator = field_validator(
        *['date'],
        mode='before'
    )(json_utils.date_validator)


class ClientPaymentCard(BaseModel):
    id: Optional[int] = None
    last_4_digits: Optional[str] = None
    expired_at: Optional[datetime.datetime] = None
    cid: Optional[str] = None
    linked_to: Optional[str] = None
    card_holder: Optional[str] = None
    debit_card: Optional[str] = None
    exp: Optional[str] = None

    _date_validator = field_validator(
        *['expired_at'],
        mode='before'
    )(json_utils.date_validator)


class ClientPaymentService(BaseModel):
    id: Optional[int] = None
    payment_system: Optional[str] = None
    date_opened: Optional[datetime.datetime] = None
    opened_by: Optional[str] = None
    email_connected: Optional[str] = None
    responsible_person: Optional[str] = None
    login_pass: Optional[str] = None
    note: Optional[str] = None

    _date_validator = field_validator(
        *['date_opened'],
        mode='before'
    )(json_utils.date_validator)


class ClientSource(BaseModel):
    id: Optional[str] = None
    title: Optional[str] = None


class ClientContact(BaseModel):
    id: Optional[int] = None
    client_person_id: Optional[str] = None
    position: Optional[str] = None
    is_main: Optional[bool] = False
    email: Optional[str] = None
    phone: Optional[str] = None
    contact_info: Optional[str] = None
    note: Optional[str] = None
    person: Optional[PersonCreate] = None


class InternalDataSource(BaseModel):
    type: Optional[str] = None
    id: Optional[str] = None


class ClientCommon(ClientBase):
    manager: Optional[ClientManager] = None
    source: Optional[ClientSource] = None
    addresses: Optional[List[ClientAddress]] = None
    # authorized_signers: Optional[List[AuthorizedSignerUpdate]] = None
    bank_accounts: Optional[List[BankAccountUpdate]] = None
    contacts: Optional[List[ClientContact]] = None
    payment_cards: Optional[List[ClientPaymentCard]] = None
    payment_services: Optional[List[ClientPaymentService]] = None
    primary_registration: Optional[ClientRegistration] = None
    secondary_registrations: Optional[List[ClientRegistration]] = None
    shares: Optional[List[ClientShare]] = None
    llc_shareholders: Optional[List[ClientLLCShareholder]] = None
    # services: Optional[List[ClientService]] = None
    # tasks: Optional[List[ClientTask]] = None
    # tax_reporting: Optional[List[ClientTaxReporting]] = None
    capitalization_table: Optional[List[ClientCapitalization]] = None

    # Internal
    internal_data_source: Optional[InternalDataSource] = None


class ClientCreate(ClientCommon):
    name: str


class ClientUpdate(ClientCommon):
    name: Optional[str] = None


class ClientApprove(BaseModel):
    note: Optional[str] = None


